import { Card, BoardCell } from './types'
import { calculateCaptures, executeMove } from './game-logic'

export interface BotMove {
  cardId: string
  cellIndex: number
  score: number
}

/**
 * Bot AI that uses a greedy strategy to maximize immediate captures
 */
export class TriadBot {
  private cards: Card[]
  private difficulty: 'easy' = 'easy'

  constructor(cards: Card[]) {
    this.cards = cards
  }

  /**
   * Calculates the best move for the bot
   */
  calculateBestMove(
    board: (BoardCell | null)[],
    botHand: string[],
    botColor: 'A' | 'B'
  ): BotMove | null {
    if (botHand.length === 0) return null

    const availableMoves: BotMove[] = []
    const cardMap = new Map(this.cards.map(card => [card.id, card]))

    // Evaluate each possible move
    for (const cardId of botHand) {
      const card = cardMap.get(cardId)
      if (!card) continue

      // Try placing this card in each empty cell
      for (let cellIndex = 0; cellIndex < 9; cellIndex++) {
        if (board[cellIndex] !== null) continue // Cell is occupied

        // Calculate the score for this move
        const score = this.evaluateMove(board, cellIndex, card, botColor)
        
        availableMoves.push({
          cardId,
          cellIndex,
          score
        })
      }
    }

    if (availableMoves.length === 0) return null

    // Sort moves by score (descending) and apply tie-breaker
    availableMoves.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score
      }
      // Tie-breaker: prefer center (4), then corners (0,2,6,8), then edges
      return this.getCellPriority(a.cellIndex) - this.getCellPriority(b.cellIndex)
    })

    return availableMoves[0]
  }

  /**
   * Evaluates the score of a potential move
   */
  private evaluateMove(
    board: (BoardCell | null)[],
    cellIndex: number,
    card: Card,
    botColor: 'A' | 'B'
  ): number {
    // Base score is the number of immediate captures
    const captures = calculateCaptures(board, cellIndex, card, botColor, this.cards)
    let score = captures.length

    // Bonus for strategic positions
    score += this.getPositionBonus(cellIndex)

    // Bonus for defensive plays (preventing opponent captures)
    score += this.getDefensiveBonus(board, cellIndex, card, botColor)

    return score
  }

  /**
   * Gets position-based bonus points
   */
  private getPositionBonus(cellIndex: number): number {
    // Center is most valuable
    if (cellIndex === 4) return 0.5
    
    // Corners are next most valuable
    if ([0, 2, 6, 8].includes(cellIndex)) return 0.3
    
    // Edges are least valuable
    return 0.1
  }

  /**
   * Gets defensive bonus for preventing opponent captures
   */
  private getDefensiveBonus(
    board: (BoardCell | null)[],
    cellIndex: number,
    card: Card,
    botColor: 'A' | 'B'
  ): number {
    const opponentColor = botColor === 'A' ? 'B' : 'A'
    let defensiveBonus = 0

    // Check if this placement would prevent opponent from capturing adjacent cells
    const adjacentCells = this.getAdjacentCells(cellIndex)
    
    for (const { index: adjIndex } of adjacentCells) {
      const adjCell = board[adjIndex]
      if (adjCell && adjCell.owner === botColor) {
        // This placement could help defend our card
        defensiveBonus += 0.2
      }
    }

    return defensiveBonus
  }

  /**
   * Gets cell priority for tie-breaking (lower number = higher priority)
   */
  private getCellPriority(cellIndex: number): number {
    // Center has highest priority (lowest number)
    if (cellIndex === 4) return 0
    
    // Corners have second priority
    if ([0, 2, 6, 8].includes(cellIndex)) return 1
    
    // Edges have lowest priority
    return 2
  }

  /**
   * Gets adjacent cell indices for a given cell
   */
  private getAdjacentCells(cellIndex: number): Array<{ index: number; direction: 'n' | 'e' | 's' | 'w' }> {
    const row = Math.floor(cellIndex / 3)
    const col = cellIndex % 3
    const adjacent: Array<{ index: number; direction: 'n' | 'e' | 's' | 'w' }> = []

    // North
    if (row > 0) {
      adjacent.push({ index: cellIndex - 3, direction: 'n' })
    }
    
    // East
    if (col < 2) {
      adjacent.push({ index: cellIndex + 1, direction: 'e' })
    }
    
    // South
    if (row < 2) {
      adjacent.push({ index: cellIndex + 3, direction: 's' })
    }
    
    // West
    if (col > 0) {
      adjacent.push({ index: cellIndex - 1, direction: 'w' })
    }

    return adjacent
  }
}

/**
 * Creates a bot player for a match
 */
export function createBotPlayer(matchId: string, deckCards: Card[], color: 'A' | 'B') {
  // Shuffle the deck and take 5 cards for the bot's hand
  const shuffled = [...deckCards].sort(() => Math.random() - 0.5)
  const hand = shuffled.slice(0, 5).map(card => card.id)

  return {
    match_id: matchId,
    user_id: null, // Bot has no user_id
    is_host: false,
    deck_id: null,
    hand,
    color,
    score: 0
  }
}
