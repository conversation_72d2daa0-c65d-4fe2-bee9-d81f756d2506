import { createServerComponentClient } from '@/lib/supabase'
import { createHand } from '@/lib/game-logic'
import { createBotPlayer } from '@/lib/bot-ai'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { match_id, host_deck_id, guest_deck_id } = body

    if (!match_id || !host_deck_id) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
    }

    // Get the match
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .select('*')
      .eq('id', match_id)
      .single()

    if (matchError || !match) {
      return NextResponse.json({ error: 'Match not found' }, { status: 404 })
    }

    if (match.status !== 'waiting') {
      return NextResponse.json({ error: 'Match is not in waiting state' }, { status: 400 })
    }

    // Get match players
    const { data: players, error: playersError } = await supabase
      .from('match_players')
      .select('*')
      .eq('match_id', match_id)

    if (playersError || !players) {
      return NextResponse.json({ error: 'Failed to get match players' }, { status: 500 })
    }

    // Verify user is the host
    const hostPlayer = players.find(p => p.is_host && p.user_id === user.id)
    if (!hostPlayer) {
      return NextResponse.json({ error: 'Only the host can start the match' }, { status: 403 })
    }

    // For PvP matches, ensure we have 2 players and guest_deck_id
    if (match.mode === 'pvp') {
      if (players.length !== 2) {
        return NextResponse.json({ error: 'Match needs 2 players to start' }, { status: 400 })
      }
      if (!guest_deck_id) {
        return NextResponse.json({ error: 'Guest deck ID is required for PvP matches' }, { status: 400 })
      }
    }

    // Get deck cards for host
    const { data: hostDeckCards, error: hostDeckError } = await supabase
      .from('deck_cards')
      .select(`
        cards (*)
      `)
      .eq('deck_id', host_deck_id)

    if (hostDeckError || !hostDeckCards) {
      return NextResponse.json({ error: 'Failed to load host deck' }, { status: 500 })
    }

    const hostCards = hostDeckCards.map((dc: any) => dc.cards)
    const hostHand = createHand(hostCards)

    // Update host player with deck and hand
    const { error: hostUpdateError } = await supabase
      .from('match_players')
      .update({
        deck_id: host_deck_id,
        hand: hostHand
      })
      .eq('match_id', match_id)
      .eq('user_id', user.id)

    if (hostUpdateError) {
      return NextResponse.json({ error: 'Failed to update host player' }, { status: 500 })
    }

    // Handle guest player (either human or bot)
    if (match.mode === 'pvp') {
      // Get guest deck cards
      const { data: guestDeckCards, error: guestDeckError } = await supabase
        .from('deck_cards')
        .select(`
          cards (*)
        `)
        .eq('deck_id', guest_deck_id)

      if (guestDeckError || !guestDeckCards) {
        return NextResponse.json({ error: 'Failed to load guest deck' }, { status: 500 })
      }

      const guestCards = guestDeckCards.map((dc: any) => dc.cards)
      const guestHand = createHand(guestCards)

      // Update guest player
      const guestPlayer = players.find(p => !p.is_host)
      if (guestPlayer) {
        const { error: guestUpdateError } = await supabase
          .from('match_players')
          .update({
            deck_id: guest_deck_id,
            hand: guestHand
          })
          .eq('id', guestPlayer.id)

        if (guestUpdateError) {
          return NextResponse.json({ error: 'Failed to update guest player' }, { status: 500 })
        }
      }
    } else if (match.mode === 'bot') {
      // Create bot player
      const botPlayer = createBotPlayer(match_id, hostCards, 'B')
      
      const { error: botError } = await supabase
        .from('match_players')
        .insert(botPlayer)

      if (botError) {
        return NextResponse.json({ error: 'Failed to create bot player' }, { status: 500 })
      }
    }

    // Update match status to active and set first turn
    const { error: matchUpdateError } = await supabase
      .from('matches')
      .update({
        status: 'active',
        current_turn_user_id: user.id, // Host starts first
        updated_at: new Date().toISOString()
      })
      .eq('id', match_id)

    if (matchUpdateError) {
      return NextResponse.json({ error: 'Failed to start match' }, { status: 500 })
    }

    return NextResponse.json({ ok: true })

  } catch (error) {
    console.error('Error starting match:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
