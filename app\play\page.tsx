'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthClient } from '@/lib/auth'

export default function PlayPage() {
  const [joinCode, setJoinCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const router = useRouter()
  const authClient = new AuthClient()

  const createPvPMatch = async () => {
    setLoading(true)
    setError('')

    try {
      // Check if user is authenticated
      const { user } = await authClient.getCurrentUser()
      if (!user) {
        router.push('/auth')
        return
      }

      const response = await fetch('/api/match/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mode: 'pvp' }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create match')
      }

      // Redirect to lobby
      router.push(`/lobby/${data.join_code}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create match')
    } finally {
      setLoading(false)
    }
  }

  const createBotMatch = async () => {
    setLoading(true)
    setError('')

    try {
      // Check if user is authenticated
      const { user } = await authClient.getCurrentUser()
      if (!user) {
        router.push('/auth')
        return
      }

      const response = await fetch('/api/match/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mode: 'bot' }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create match')
      }

      // Redirect to lobby (bot matches still use lobby for deck selection)
      router.push(`/lobby/${data.match_id}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create match')
    } finally {
      setLoading(false)
    }
  }

  const joinMatch = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!joinCode.trim()) {
      setError('Please enter a join code')
      setLoading(false)
      return
    }

    try {
      // Check if user is authenticated
      const { user } = await authClient.getCurrentUser()
      if (!user) {
        router.push('/auth')
        return
      }

      const response = await fetch('/api/match/join', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ join_code: joinCode.trim().toUpperCase() }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to join match')
      }

      // Redirect to lobby
      router.push(`/lobby/${joinCode.trim().toUpperCase()}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join match')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Choose Your Battle
          </h1>
          <p className="text-gray-300 text-lg">
            Create a new match or join an existing one
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-8 p-4 bg-red-900 border border-red-700 rounded-lg text-red-200 text-center">
            {error}
          </div>
        )}

        {/* Game Mode Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Create PvP Match */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 hover:border-purple-500 transition-colors">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Play vs Friend
              </h3>
              <p className="text-gray-400 mb-6">
                Create a match and share the join code with a friend
              </p>
              <button
                onClick={createPvPMatch}
                disabled={loading}
                className="w-full py-3 px-6 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create PvP Match'}
              </button>
            </div>
          </div>

          {/* Create Bot Match */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 hover:border-blue-500 transition-colors">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Play vs Bot
              </h3>
              <p className="text-gray-400 mb-6">
                Practice against an AI opponent with greedy strategy
              </p>
              <button
                onClick={createBotMatch}
                disabled={loading}
                className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Play vs Bot'}
              </button>
            </div>
          </div>
        </div>

        {/* Join Match */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <div className="text-center mb-6">
            <h3 className="text-xl font-semibold text-white mb-2">
              Join a Match
            </h3>
            <p className="text-gray-400">
              Enter a 6-character join code to join a friend's match
            </p>
          </div>

          <form onSubmit={joinMatch} className="max-w-md mx-auto">
            <div className="flex space-x-4">
              <input
                type="text"
                value={joinCode}
                onChange={(e) => setJoinCode(e.target.value.toUpperCase())}
                placeholder="Enter join code"
                maxLength={6}
                className="flex-1 px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
              <button
                type="submit"
                disabled={loading || !joinCode.trim()}
                className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Joining...' : 'Join'}
              </button>
            </div>
          </form>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-12">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  )
}
