'use client'

import { useState, useEffect } from 'react'
import { Deck, Card } from '@/lib/types'
import { createClientComponentClient } from '@/lib/supabase'
import CardMini from './CardMini'
import Image from 'next/image'

interface DeckSelectProps {
  selectedDeckId?: string
  onDeckSelect: (deckId: string) => void
  disabled?: boolean
}

export default function DeckSelect({ selectedDeckId, onDeckSelect, disabled = false }: DeckSelectProps) {
  const [decks, setDecks] = useState<Deck[]>([])
  const [deckCards, setDeckCards] = useState<Record<string, Card[]>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [expandedDeck, setExpandedDeck] = useState<string | null>(null)

  const supabase = createClientComponentClient()

  useEffect(() => {
    loadDecks()
  }, [])

  const loadDecks = async () => {
    try {
      // Load decks
      const { data: decksData, error: decksError } = await supabase
        .from('decks')
        .select('*')
        .order('name')

      if (decksError) throw decksError

      setDecks(decksData || [])

      // Load cards for each deck
      const deckCardsMap: Record<string, Card[]> = {}
      
      for (const deck of decksData || []) {
        const { data: cards, error: cardsError } = await supabase
          .from('deck_cards')
          .select(`
            quantity,
            cards (*)
          `)
          .eq('deck_id', deck.id)

        if (cardsError) {
          console.error('Error loading cards for deck:', deck.name, cardsError)
          continue
        }

        deckCardsMap[deck.id] = cards?.map((dc: any) => dc.cards) || []
      }

      setDeckCards(deckCardsMap)
    } catch (err) {
      console.error('Error loading decks:', err)
      setError('Failed to load decks')
    } finally {
      setLoading(false)
    }
  }

  const toggleDeckExpansion = (deckId: string) => {
    setExpandedDeck(expandedDeck === deckId ? null : deckId)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-gray-400">Loading decks...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-red-400 text-center py-8">
        {error}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white mb-4">Choose Your Deck</h3>
      
      {decks.map((deck) => (
        <div
          key={deck.id}
          className={`
            border-2 rounded-lg p-4 transition-all
            ${selectedDeckId === deck.id 
              ? 'border-purple-500 bg-purple-900 bg-opacity-30' 
              : 'border-gray-600 bg-gray-800 hover:border-gray-500'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
          onClick={() => !disabled && onDeckSelect(deck.id)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Deck Thumbnail */}
              {deck.thumbnail_url ? (
                <Image
                  src={deck.thumbnail_url}
                  alt={deck.name}
                  width={48}
                  height={48}
                  className="rounded"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.src = '/decks/placeholder.png'
                  }}
                />
              ) : (
                <div className="w-12 h-12 bg-gray-700 rounded flex items-center justify-center">
                  <span className="text-gray-400 text-xs">DECK</span>
                </div>
              )}

              <div>
                <h4 className="text-white font-semibold">{deck.name}</h4>
                <p className="text-gray-400 text-sm">{deck.description}</p>
                <p className="text-gray-500 text-xs">
                  {deckCards[deck.id]?.length || 0} cards
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {selectedDeckId === deck.id && (
                <div className="text-purple-400">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}

              <button
                onClick={(e) => {
                  e.stopPropagation()
                  toggleDeckExpansion(deck.id)
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg 
                  className={`w-5 h-5 transition-transform ${expandedDeck === deck.id ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          </div>

          {/* Expanded Card View */}
          {expandedDeck === deck.id && deckCards[deck.id] && (
            <div className="mt-4 pt-4 border-t border-gray-600">
              <div className="grid grid-cols-5 gap-2">
                {deckCards[deck.id].map((card, index) => (
                  <CardMini
                    key={`${card.id}-${index}`}
                    card={card}
                    size="small"
                    showStats={true}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
