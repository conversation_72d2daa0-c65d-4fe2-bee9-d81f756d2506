export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          avatar_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          username: string
          avatar_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          username?: string
          avatar_url?: string | null
          created_at?: string
        }
      }
      stats: {
        Row: {
          user_id: string
          wins: number
          losses: number
          draws: number
          most_used_deck_id: string | null
          updated_at: string
        }
        Insert: {
          user_id: string
          wins?: number
          losses?: number
          draws?: number
          most_used_deck_id?: string | null
          updated_at?: string
        }
        Update: {
          user_id?: string
          wins?: number
          losses?: number
          draws?: number
          most_used_deck_id?: string | null
          updated_at?: string
        }
      }
      cards: {
        Row: {
          id: string
          slug: string
          name: string
          faction: string | null
          art_url: string | null
          n: number | null
          e: number | null
          s: number | null
          w: number | null
          rarity: string | null
          ability_key: string | null
          meta: any | null
        }
        Insert: {
          id?: string
          slug: string
          name: string
          faction?: string | null
          art_url?: string | null
          n?: number | null
          e?: number | null
          s?: number | null
          w?: number | null
          rarity?: string | null
          ability_key?: string | null
          meta?: any | null
        }
        Update: {
          id?: string
          slug?: string
          name?: string
          faction?: string | null
          art_url?: string | null
          n?: number | null
          e?: number | null
          s?: number | null
          w?: number | null
          rarity?: string | null
          ability_key?: string | null
          meta?: any | null
        }
      }
      decks: {
        Row: {
          id: string
          slug: string
          name: string
          description: string | null
          thumbnail_url: string | null
        }
        Insert: {
          id?: string
          slug: string
          name: string
          description?: string | null
          thumbnail_url?: string | null
        }
        Update: {
          id?: string
          slug?: string
          name?: string
          description?: string | null
          thumbnail_url?: string | null
        }
      }
      deck_cards: {
        Row: {
          id: string
          deck_id: string
          card_id: string
          quantity: number
        }
        Insert: {
          id?: string
          deck_id: string
          card_id: string
          quantity?: number
        }
        Update: {
          id?: string
          deck_id?: string
          card_id?: string
          quantity?: number
        }
      }
      matches: {
        Row: {
          id: string
          status: string
          mode: string
          join_code: string | null
          board_state: any
          rules: any | null
          current_turn_user_id: string | null
          winner_user_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          status?: string
          mode?: string
          join_code?: string | null
          board_state?: any
          rules?: any | null
          current_turn_user_id?: string | null
          winner_user_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          status?: string
          mode?: string
          join_code?: string | null
          board_state?: any
          rules?: any | null
          current_turn_user_id?: string | null
          winner_user_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      match_players: {
        Row: {
          id: string
          match_id: string
          user_id: string | null
          is_host: boolean
          deck_id: string | null
          hand: any | null
          color: string | null
          score: number
        }
        Insert: {
          id?: string
          match_id: string
          user_id?: string | null
          is_host?: boolean
          deck_id?: string | null
          hand?: any | null
          color?: string | null
          score?: number
        }
        Update: {
          id?: string
          match_id?: string
          user_id?: string | null
          is_host?: boolean
          deck_id?: string | null
          hand?: any | null
          color?: string | null
          score?: number
        }
      }
      moves: {
        Row: {
          id: string
          match_id: string
          user_id: string | null
          card_id: string | null
          cell_index: number | null
          captures: any | null
          created_at: string
        }
        Insert: {
          id?: string
          match_id: string
          user_id?: string | null
          card_id?: string | null
          cell_index?: number | null
          captures?: any | null
          created_at?: string
        }
        Update: {
          id?: string
          match_id?: string
          user_id?: string | null
          card_id?: string | null
          cell_index?: number | null
          captures?: any | null
          created_at?: string
        }
      }
    }
  }
}

// Game-specific types
export interface Card {
  id: string
  slug: string
  name: string
  faction?: string
  art_url?: string
  n: number
  e: number
  s: number
  w: number
  rarity?: string
  ability_key?: string
  meta?: any
}

export interface Deck {
  id: string
  slug: string
  name: string
  description?: string
  thumbnail_url?: string
  cards?: Card[]
}

export interface BoardCell {
  owner: 'A' | 'B' | null
  card_id: string | null
}

export interface GameMatch {
  id: string
  status: 'waiting' | 'active' | 'finished' | 'cancelled'
  mode: 'pvp' | 'bot'
  join_code?: string
  board_state: {
    cells: (BoardCell | null)[]
  }
  rules?: any
  current_turn_user_id?: string
  winner_user_id?: string
  created_at: string
  updated_at: string
  players?: MatchPlayer[]
}

export interface MatchPlayer {
  id: string
  match_id: string
  user_id?: string
  is_host: boolean
  deck_id?: string
  hand?: string[]
  color: 'A' | 'B'
  score: number
}

export interface Move {
  id: string
  match_id: string
  user_id?: string
  card_id?: string
  cell_index: number
  captures?: number[]
  created_at: string
}
