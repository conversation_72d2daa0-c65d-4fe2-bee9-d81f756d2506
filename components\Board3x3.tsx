'use client'

import { BoardCell, Card } from '@/lib/types'
import CardMini from './CardMini'

interface Board3x3Props {
  boardState: (BoardCell | null)[]
  cards: Card[]
  onCellClick: (cellIndex: number) => void
  isMyTurn: boolean
  legalCells: number[]
  playerColor: 'A' | 'B'
}

export default function Board3x3({ 
  boardState, 
  cards, 
  onCellClick, 
  isMyTurn, 
  legalCells,
  playerColor 
}: Board3x3Props) {
  const cardMap = new Map(cards.map(card => [card.id, card]))

  const getCellColor = (cell: BoardCell | null) => {
    if (!cell) return 'bg-gray-700 border-gray-600'
    
    if (cell.owner === 'A') {
      return playerColor === 'A' 
        ? 'bg-blue-900 border-blue-500' 
        : 'bg-red-900 border-red-500'
    } else {
      return playerColor === 'B' 
        ? 'bg-blue-900 border-blue-500' 
        : 'bg-red-900 border-red-500'
    }
  }

  const getCellHoverEffect = (cellIndex: number, cell: BoardCell | null) => {
    if (!isMyTurn || cell !== null) return ''
    
    if (legalCells.includes(cellIndex)) {
      return 'hover:bg-green-800 hover:border-green-500 cursor-pointer'
    }
    
    return 'cursor-not-allowed opacity-50'
  }

  return (
    <div className="flex flex-col items-center">
      {/* Board Title */}
      <div className="mb-4 text-center">
        <h3 className="text-lg font-semibold text-white">
          Battle Arena
        </h3>
        <p className="text-gray-400 text-sm">
          {isMyTurn ? 'Your turn - Select a card and place it' : 'Opponent\'s turn'}
        </p>
      </div>

      {/* 3x3 Grid */}
      <div className="grid grid-cols-3 gap-2 p-4 bg-gray-800 rounded-lg border border-gray-600">
        {boardState.map((cell, index) => (
          <div
            key={index}
            className={`
              w-24 h-32 border-2 rounded-lg flex items-center justify-center
              transition-all duration-200
              ${getCellColor(cell)}
              ${getCellHoverEffect(index, cell)}
            `}
            onClick={() => {
              if (isMyTurn && cell === null && legalCells.includes(index)) {
                onCellClick(index)
              }
            }}
          >
            {cell && cell.card_id ? (
              <div className="relative">
                {/* Card */}
                <CardMini
                  card={cardMap.get(cell.card_id)!}
                  size="small"
                  showStats={true}
                />
                
                {/* Owner indicator */}
                <div className={`
                  absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white
                  ${cell.owner === 'A' 
                    ? (playerColor === 'A' ? 'bg-blue-500' : 'bg-red-500')
                    : (playerColor === 'B' ? 'bg-blue-500' : 'bg-red-500')
                  }
                `} />
              </div>
            ) : (
              <div className="text-gray-500 text-xs text-center">
                {isMyTurn && legalCells.includes(index) ? (
                  <div className="flex flex-col items-center">
                    <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span>Place</span>
                  </div>
                ) : (
                  <span>Empty</span>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Grid Position Labels (for debugging/reference) */}
      <div className="mt-2 text-xs text-gray-500 grid grid-cols-3 gap-2 w-full max-w-xs text-center">
        {Array.from({ length: 9 }, (_, i) => (
          <div key={i}>{i}</div>
        ))}
      </div>

      {/* Score Display */}
      <div className="mt-4 flex space-x-8 text-center">
        <div className={`
          px-4 py-2 rounded-lg
          ${playerColor === 'A' ? 'bg-blue-900 border border-blue-500' : 'bg-gray-700 border border-gray-600'}
        `}>
          <div className="text-white font-semibold">
            {boardState.filter(cell => cell?.owner === 'A').length}
          </div>
          <div className="text-gray-400 text-xs">
            {playerColor === 'A' ? 'You' : 'Opponent'}
          </div>
        </div>

        <div className="flex items-center text-gray-400">
          <span>vs</span>
        </div>

        <div className={`
          px-4 py-2 rounded-lg
          ${playerColor === 'B' ? 'bg-blue-900 border border-blue-500' : 'bg-gray-700 border border-gray-600'}
        `}>
          <div className="text-white font-semibold">
            {boardState.filter(cell => cell?.owner === 'B').length}
          </div>
          <div className="text-gray-400 text-xs">
            {playerColor === 'B' ? 'You' : 'Opponent'}
          </div>
        </div>
      </div>
    </div>
  )
}
