import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Paragons: Triad
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Experience the strategic depth of Triple Triad with your favorite Paragons Age of Champions cards
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/play"
                className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors"
              >
                Start Playing
              </Link>
              <Link
                href="/decks"
                className="inline-flex items-center px-8 py-4 border border-gray-600 text-lg font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
              >
                View Decks
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 bg-gray-900 bg-opacity-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              How to Play
            </h2>
            <p className="text-gray-300 text-lg">
              Master the art of strategic card placement
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Choose Your Deck
              </h3>
              <p className="text-gray-400">
                Select from prebuilt decks featuring different strategies and card combinations
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Place Your Cards
              </h3>
              <p className="text-gray-400">
                Take turns placing cards on the 3x3 grid. Higher values capture adjacent opponent cards
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Control the Board
              </h3>
              <p className="text-gray-400">
                Win by controlling more cards than your opponent when all 9 spaces are filled
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* Game Modes Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Game Modes
            </h2>
            <p className="text-gray-300 text-lg">
              Challenge friends or test your skills against AI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white">
                  Player vs Player
                </h3>
              </div>
              <p className="text-gray-400 mb-4">
                Create a match and share the join code with a friend for real-time strategic battles
              </p>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Real-time multiplayer</li>
                <li>• 6-character join codes</li>
                <li>• Live match updates</li>
              </ul>
            </div>

            <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white">
                  Player vs Bot
                </h3>
              </div>
              <p className="text-gray-400 mb-4">
                Practice your skills against an AI opponent that uses greedy capture strategy
              </p>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Instant matchmaking</li>
                <li>• Strategic AI opponent</li>
                <li>• Perfect for practice</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-400">
            <p>
              Built with ❤️ for the Paragons Age of Champions community
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
