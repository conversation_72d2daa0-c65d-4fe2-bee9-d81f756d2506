-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE decks ENABLE ROW LEVEL SECURITY;
ALTER TABLE deck_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_players ENABLE ROW LEVEL SECURITY;
ALTER TABLE moves ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Enable read access for all users" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Stats policies
CREATE POLICY "Enable read access for all users" ON stats
  FOR SELECT USING (true);

CREATE POLICY "Users can update own stats" ON stats
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can insert own stats" ON stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Cards policies (read-only for users)
CREATE POLICY "Enable read access for all users" ON cards
  FOR SELECT USING (true);

-- Decks policies (read-only for users)
CREATE POLICY "Enable read access for all users" ON decks
  FOR SELECT USING (true);

-- Deck_cards policies (read-only for users)
CREATE POLICY "Enable read access for all users" ON deck_cards
  FOR SELECT USING (true);

-- Matches policies
CREATE POLICY "Enable read access for all users" ON matches
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create matches" ON matches
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update matches they participate in" ON matches
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM match_players 
      WHERE match_players.match_id = matches.id 
      AND match_players.user_id = auth.uid()
    )
  );

-- Match_players policies
CREATE POLICY "Enable read access for all users" ON match_players
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can join matches" ON match_players
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update their own match player record" ON match_players
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Moves policies
CREATE POLICY "Enable read access for all users" ON moves
  FOR SELECT USING (true);

CREATE POLICY "Users can insert moves in their matches" ON moves
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM match_players 
      WHERE match_players.match_id = moves.match_id 
      AND match_players.user_id = auth.uid()
    )
  );
