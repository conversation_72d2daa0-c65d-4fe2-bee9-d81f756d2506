'use client'

import { Card } from '@/lib/types'
import CardMini from './CardMini'

interface HandPanelProps {
  hand: string[]
  cards: Card[]
  selectedCardId: string | null
  onCardSelect: (cardId: string) => void
  isMyTurn: boolean
  disabled?: boolean
}

export default function HandPanel({ 
  hand, 
  cards, 
  selectedCardId, 
  onCardSelect, 
  isMyTurn,
  disabled = false 
}: HandPanelProps) {
  const cardMap = new Map(cards.map(card => [card.id, card]))

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
      {/* Header */}
      <div className="mb-4 text-center">
        <h3 className="text-lg font-semibold text-white">
          Your Hand
        </h3>
        <p className="text-gray-400 text-sm">
          {isMyTurn 
            ? 'Select a card to place on the board' 
            : 'Wait for your opponent\'s move'
          }
        </p>
      </div>

      {/* Cards */}
      <div className="flex justify-center space-x-3">
        {hand.map((cardId, index) => {
          const card = cardMap.get(cardId)
          if (!card) return null

          const isSelected = selectedCardId === cardId
          const canSelect = isMyTurn && !disabled

          return (
            <div
              key={`${cardId}-${index}`}
              className={`
                relative transition-all duration-200
                ${isSelected 
                  ? 'transform -translate-y-2 ring-2 ring-purple-500' 
                  : canSelect 
                    ? 'hover:transform hover:-translate-y-1 cursor-pointer' 
                    : 'opacity-50 cursor-not-allowed'
                }
              `}
              onClick={() => {
                if (canSelect) {
                  onCardSelect(cardId)
                }
              }}
            >
              <CardMini
                card={card}
                size="medium"
                showStats={true}
                className={isSelected ? 'ring-2 ring-purple-500' : ''}
              />

              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}

              {/* Card position indicator */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-gray-700 text-white text-xs px-2 py-1 rounded">
                {index + 1}
              </div>
            </div>
          )
        })}
      </div>

      {/* Instructions */}
      {isMyTurn && !disabled && (
        <div className="mt-4 text-center">
          <p className="text-gray-400 text-sm">
            {selectedCardId 
              ? 'Now click on an empty cell to place your card' 
              : 'Click on a card to select it'
            }
          </p>
        </div>
      )}

      {/* Hand count */}
      <div className="mt-4 text-center">
        <p className="text-gray-500 text-xs">
          {hand.length} cards remaining
        </p>
      </div>
    </div>
  )
}
