'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createClientComponentClient } from '@/lib/supabase'
import { AuthClient } from '@/lib/auth'
import { GameMatch, MatchPlayer } from '@/lib/types'
import DeckSelect from '@/components/DeckSelect'

export default function LobbyPage() {
  const [match, setMatch] = useState<GameMatch | null>(null)
  const [players, setPlayers] = useState<MatchPlayer[]>([])
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [selectedDeckId, setSelectedDeckId] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [starting, setStarting] = useState(false)

  const router = useRouter()
  const params = useParams()
  const code = params.code as string
  
  const supabase = createClientComponentClient()
  const authClient = new AuthClient()

  useEffect(() => {
    loadLobbyData()
    
    // Set up real-time subscriptions
    const matchSubscription = supabase
      .channel(`match-${code}`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'matches', filter: `join_code=eq.${code}` },
        (payload) => {
          if (payload.eventType === 'UPDATE') {
            setMatch(payload.new as GameMatch)
            
            // If match started, redirect to game
            if (payload.new.status === 'active') {
              router.push(`/match/${payload.new.id}`)
            }
          }
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_players' },
        () => {
          loadPlayers()
        }
      )
      .subscribe()

    return () => {
      matchSubscription.unsubscribe()
    }
  }, [code])

  const loadLobbyData = async () => {
    try {
      // Get current user
      const { user } = await authClient.getCurrentUser()
      if (!user) {
        router.push('/auth')
        return
      }
      setCurrentUser(user)

      // Load match by join code or ID
      let matchQuery = supabase.from('matches').select('*')
      
      // Try join code first, then ID
      if (code.length === 6) {
        matchQuery = matchQuery.eq('join_code', code)
      } else {
        matchQuery = matchQuery.eq('id', code)
      }

      const { data: matchData, error: matchError } = await matchQuery.single()

      if (matchError || !matchData) {
        setError('Match not found')
        return
      }

      setMatch(matchData)
      await loadPlayers(matchData.id)

    } catch (err) {
      console.error('Error loading lobby:', err)
      setError('Failed to load lobby')
    } finally {
      setLoading(false)
    }
  }

  const loadPlayers = async (matchId?: string) => {
    const id = matchId || match?.id
    if (!id) return

    const { data: playersData, error } = await supabase
      .from('match_players')
      .select(`
        *,
        profiles (username, avatar_url)
      `)
      .eq('match_id', id)

    if (!error && playersData) {
      setPlayers(playersData)
    }
  }

  const startMatch = async () => {
    if (!match || !currentUser || !selectedDeckId) return

    setStarting(true)
    setError('')

    try {
      const isHost = players.find(p => p.user_id === currentUser.id)?.is_host
      if (!isHost) {
        setError('Only the host can start the match')
        return
      }

      // For PvP matches, need guest deck selection
      let guestDeckId = selectedDeckId
      if (match.mode === 'pvp') {
        const guestPlayer = players.find(p => !p.is_host)
        if (!guestPlayer) {
          setError('Waiting for another player to join')
          return
        }
        // For now, use the same deck for guest (in a real implementation, guest would select their own)
        guestDeckId = selectedDeckId
      }

      const response = await fetch('/api/match/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          match_id: match.id,
          host_deck_id: selectedDeckId,
          guest_deck_id: match.mode === 'pvp' ? guestDeckId : undefined
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start match')
      }

      // Redirect will happen via real-time subscription
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start match')
    } finally {
      setStarting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading lobby...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-lg mb-4">{error}</div>
          <a
            href="/play"
            className="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Play
          </a>
        </div>
      </div>
    )
  }

  const currentPlayer = players.find(p => p.user_id === currentUser?.id)
  const isHost = currentPlayer?.is_host || false
  const canStart = isHost && selectedDeckId && (match?.mode === 'bot' || players.length === 2)

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Game Lobby
          </h1>
          {match?.join_code && (
            <div className="text-lg text-gray-300">
              Join Code: <span className="font-mono bg-gray-800 px-3 py-1 rounded">{match.join_code}</span>
            </div>
          )}
          <div className="text-gray-400 mt-2">
            {match?.mode === 'pvp' ? 'Player vs Player' : 'Player vs Bot'}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-900 border border-red-700 rounded-lg text-red-200 text-center">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Players Panel */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Players</h3>
            
            <div className="space-y-4">
              {players.map((player, index) => (
                <div
                  key={player.id}
                  className={`
                    flex items-center justify-between p-4 rounded-lg
                    ${player.user_id === currentUser?.id 
                      ? 'bg-purple-900 border border-purple-500' 
                      : 'bg-gray-700'
                    }
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold
                      ${player.color === 'A' ? 'bg-blue-600' : 'bg-red-600'}
                    `}>
                      {player.color}
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        {player.user_id ? (player as any).profiles?.username || 'Player' : 'Bot'}
                      </div>
                      <div className="text-gray-400 text-sm">
                        {player.is_host ? 'Host' : 'Guest'}
                      </div>
                    </div>
                  </div>
                  
                  {player.user_id === currentUser?.id && (
                    <div className="text-purple-400">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              ))}

              {/* Waiting for player slot */}
              {match?.mode === 'pvp' && players.length < 2 && (
                <div className="flex items-center justify-center p-4 border-2 border-dashed border-gray-600 rounded-lg">
                  <div className="text-gray-400 text-center">
                    <div className="text-lg mb-1">Waiting for opponent...</div>
                    <div className="text-sm">Share the join code with a friend</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Deck Selection */}
          <div>
            <DeckSelect
              selectedDeckId={selectedDeckId}
              onDeckSelect={setSelectedDeckId}
              disabled={!isHost}
            />

            {/* Start Button */}
            {isHost && (
              <div className="mt-6">
                <button
                  onClick={startMatch}
                  disabled={!canStart || starting}
                  className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {starting ? 'Starting Match...' : 'Start Match'}
                </button>
                
                {!selectedDeckId && (
                  <p className="text-gray-400 text-sm mt-2 text-center">
                    Select a deck to start the match
                  </p>
                )}
                
                {match?.mode === 'pvp' && players.length < 2 && (
                  <p className="text-gray-400 text-sm mt-2 text-center">
                    Waiting for another player to join
                  </p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Back Button */}
        <div className="text-center mt-8">
          <a
            href="/play"
            className="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Play
          </a>
        </div>
      </div>
    </div>
  )
}
