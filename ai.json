{"project": {"name": "paragons-triple-triad", "description": "A lightweight Triple Triad–inspired web game using Paragons: Age of Champions card art. PvP by join code or vs. bot. Prebuilt decks only. Simple accounts, stats tracking.", "repo": "github.com/your-org/paragons-triple-triad", "brand": {"title": "Paragons: <PERSON>ad", "accentColor": "#5C3D99", "secondaryColor": "#38A3A5", "logoPath": "/logo.svg"}}, "stack": {"frontend": ["Next.js", "React", "TailwindCSS"], "backend": ["Next.js API Routes"], "db": "Supabase (Postgres + Auth + Realtime)", "hosting": "<PERSON><PERSON><PERSON> (suggested)"}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "YOUR_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "YOUR_SUPABASE_ANON_KEY"}, "db": {"schemas": [{"name": "public", "tables": [{"name": "profiles", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "auth.uid()", "constraints": ["primary key"]}, {"name": "username", "type": "text", "constraints": ["unique", "not null"]}, {"name": "avatar_url", "type": "text"}, {"name": "created_at", "type": "timestamp with time zone", "default": "now()"}], "rls": [{"policy": "enable read for all", "statement": "using (true)"}, {"policy": "owner can update self", "statement": "using (auth.uid() = id) with check (auth.uid() = id)"}]}, {"name": "stats", "pk": "user_id", "columns": [{"name": "user_id", "type": "uuid", "constraints": ["primary key", "references profiles(id)"]}, {"name": "wins", "type": "int", "default": 0}, {"name": "losses", "type": "int", "default": 0}, {"name": "draws", "type": "int", "default": 0}, {"name": "most_used_deck_id", "type": "uuid"}, {"name": "updated_at", "type": "timestamp with time zone", "default": "now()"}], "rls": [{"policy": "public read", "statement": "using (true)"}, {"policy": "owner can update", "statement": "using (auth.uid() = user_id) with check (auth.uid() = user_id)"}]}, {"name": "cards", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "slug", "type": "text", "constraints": ["unique", "not null"]}, {"name": "name", "type": "text", "constraints": ["not null"]}, {"name": "faction", "type": "text", "comment": "Optional: e.g., Paragon faction"}, {"name": "art_url", "type": "text", "comment": "Path or URL to card art"}, {"name": "n", "type": "int", "comment": "Top value (0-10)"}, {"name": "e", "type": "int", "comment": "Right value (0-10)"}, {"name": "s", "type": "int", "comment": "Bottom value (0-10)"}, {"name": "w", "type": "int", "comment": "Left value (0-10)"}, {"name": "rarity", "type": "text", "comment": "Optional"}, {"name": "ability_key", "type": "text", "comment": "Optional—future-proofing"}, {"name": "meta", "type": "jsonb", "comment": "Any extra data"}]}, {"name": "decks", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "slug", "type": "text", "constraints": ["unique", "not null"]}, {"name": "name", "type": "text", "constraints": ["not null"]}, {"name": "description", "type": "text"}, {"name": "thumbnail_url", "type": "text"}]}, {"name": "deck_cards", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "deck_id", "type": "uuid", "constraints": ["not null", "references decks(id) on delete cascade"]}, {"name": "card_id", "type": "uuid", "constraints": ["not null", "references cards(id) on delete cascade"]}, {"name": "quantity", "type": "int", "default": 1}], "indexes": [{"name": "deck_cards_unique", "definition": "unique(deck_id, card_id)"}]}, {"name": "matches", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "status", "type": "text", "default": "waiting", "comment": "waiting | active | finished | cancelled"}, {"name": "mode", "type": "text", "default": "pvp", "comment": "pvp | bot"}, {"name": "join_code", "type": "text", "constraints": ["unique"], "comment": "6-char code for friend joins"}, {"name": "board_state", "type": "jsonb", "default": "{\"cells\":[null,null,null,null,null,null,null,null,null]}", "comment": "9 cells, each null or {owner, card_id}"}, {"name": "rules", "type": "jsonb", "comment": "House rules toggles"}, {"name": "current_turn_user_id", "type": "uuid"}, {"name": "winner_user_id", "type": "uuid"}, {"name": "created_at", "type": "timestamp with time zone", "default": "now()"}, {"name": "updated_at", "type": "timestamp with time zone", "default": "now()"}], "rls": [{"policy": "read if participant or public waiting", "statement": "using (true)"}, {"policy": "insert by authenticated", "statement": "with check (auth.role() = 'authenticated')"}, {"policy": "update if participant", "statement": "using (true) with check (true)"}]}, {"name": "match_players", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "match_id", "type": "uuid", "constraints": ["references matches(id) on delete cascade", "not null"]}, {"name": "user_id", "type": "uuid", "comment": "null for bot", "constraints": ["references profiles(id)"]}, {"name": "is_host", "type": "boolean", "default": false}, {"name": "deck_id", "type": "uuid", "constraints": ["references decks(id)"]}, {"name": "hand", "type": "jsonb", "comment": "array of 5 card_ids"}, {"name": "color", "type": "text", "comment": "A or B"}, {"name": "score", "type": "int", "default": 0}], "indexes": [{"name": "match_players_match_idx", "definition": "index(match_id)"}]}, {"name": "moves", "pk": "id", "columns": [{"name": "id", "type": "uuid", "default": "gen_random_uuid()", "constraints": ["primary key"]}, {"name": "match_id", "type": "uuid", "constraints": ["references matches(id) on delete cascade", "not null"]}, {"name": "user_id", "type": "uuid"}, {"name": "card_id", "type": "uuid", "constraints": ["references cards(id)"]}, {"name": "cell_index", "type": "int", "comment": "0..8"}, {"name": "captures", "type": "jsonb", "comment": "array of cell indexes captured"}, {"name": "created_at", "type": "timestamp with time zone", "default": "now()"}], "indexes": [{"name": "moves_match_idx", "definition": "index(match_id)"}]}]}]}, "security": {"auth": "Supabase email magic links (lightweight)", "rules": "Public can view lobbies and finished matches; only participants can mutate their match. Profiles self-edit. Stats owner-edit."}, "realtime": {"channels": [{"name": "match:{match_id}", "events": ["match_updated", "move_made", "player_joined", "finished"]}]}, "game": {"board": {"rows": 3, "cols": 3, "cells": 9, "indexing": "0..8 left-to-right, top-to-bottom"}, "handSize": 5, "captureRules": {"basic": true, "same": false, "plus": false, "combo": false, "reverse": false, "fallenAce": false}, "turnOrder": "Host starts by default; alternate turns.", "scoring": "At game end, player controls more cells than opponent → win. Equal → draw.", "cardSchemaNotes": "Each card has N,E,S,W integers (0-10). Compare touching edges on placement; higher value captures."}, "matchmaking": {"pvp": {"flow": "Host creates match → system generates 6-char join_code → host shares code → guest joins → both pick prebuilt decks → start.", "joinCode": {"length": 6, "charset": "A-Z0-9", "collisionRetries": 5}}, "bot": {"flow": "User chooses 'Play vs Bot' → select deck → bot auto-joins → game starts.", "difficulty": "easy", "aiStrategy": "Greedy: pick the move that maximizes immediate captures; tie-breaker prefers center (4), then corners (0,2,6,8), then edges."}}, "ui": {"routes": [{"path": "/", "components": ["Hero", "CTAButtons", "HowToPlay", "DeckGalleryPreview", "Footer"]}, {"path": "/play", "components": ["ModeSelect", "CreateLobbyCard", "Join<PERSON><PERSON>byCard"]}, {"path": "/lobby/[code]", "components": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "PlayerSlots", "DeckSelect", "RulesToggle (read-only for now)", "StartButton (host only)"]}, {"path": "/match/[id]", "components": ["MatchHeader", "Board3x3", "HandPanel", "MoveLog", "ScoreBar", "ChatMinimal (optional)", "ForfeitButton"]}, {"path": "/profile/[username]", "components": ["Avatar", "StatsSummary", "RecentMatches", "MostUsedDeck"]}, {"path": "/decks", "components": ["DeckCards", "DeckDetailsModal"]}, {"path": "/auth", "components": ["MagicLinkForm"]}], "components": {"Board3x3": {"props": ["boardState", "selectCell(index)", "isMyTurn", "legalCells"], "render": "3x3 grid; each cell shows owner color and card art thumbnail."}, "CardMini": {"props": ["name", "n", "e", "s", "w", "art_url", "rarity"], "note": "Show side numbers overlaid; use placeholder art if missing."}}}, "apiRoutes": [{"path": "/api/match/create", "method": "POST", "body": {"mode": "pvp|bot"}, "returns": {"match_id": "uuid", "join_code": "string|null"}}, {"path": "/api/match/join", "method": "POST", "body": {"join_code": "string"}, "returns": {"match_id": "uuid"}}, {"path": "/api/match/start", "method": "POST", "body": {"match_id": "uuid", "host_deck_id": "uuid", "guest_deck_id": "uuid"}, "returns": {"ok": true}}, {"path": "/api/move", "method": "POST", "body": {"match_id": "uuid", "card_id": "uuid", "cell_index": "int"}, "returns": {"ok": true, "captures": [0]}}, {"path": "/api/bot/move", "method": "POST", "body": {"match_id": "uuid"}, "returns": {"ok": true}}], "logic": {"moveValidation": "Reject if not player's turn, cell occupied, or card not in hand.", "captureComputation": "On placement, check neighbors (N,E,S,W). If placed edge > adjacent opponent edge (opposing side), flip ownership.", "endCondition": "After 9th placement, compute scores and set winner_user_id or draw.", "statsUpdate": "On match finish, increment wins/losses/draws and update most_used_deck_id (mode by frequency)."}, "seed": {"cards": [{"slug": "paragon-sentinel", "name": "Sentinel", "faction": "Paragons", "art_url": "/cards/paragons/sentinel.png", "n": 7, "e": 3, "s": 5, "w": 6, "rarity": "R", "meta": {"blurb": "Solid north/west wall."}}, {"slug": "paragon-archon", "name": "<PERSON><PERSON>", "faction": "Paragons", "art_url": "/cards/paragons/archon.png", "n": 4, "e": 8, "s": 6, "w": 2, "rarity": "SR", "meta": {"blurb": "Right-side powerhouse."}}, {"slug": "paragon-vanguard", "name": "Vanguard", "faction": "Paragons", "art_url": "/cards/paragons/vanguard.png", "n": 5, "e": 5, "s": 5, "w": 5, "rarity": "C", "meta": {"blurb": "Balanced."}}, {"slug": "paragon-shade", "name": "Shade", "faction": "Paragons", "art_url": "/cards/paragons/shade.png", "n": 2, "e": 7, "s": 7, "w": 3, "rarity": "R"}, {"slug": "paragon-colossus", "name": "Coloss<PERSON>", "faction": "Paragons", "art_url": "/cards/paragons/colossus.png", "n": 8, "e": 2, "s": 8, "w": 1, "rarity": "SR"}, {"slug": "paragon-ranger", "name": "<PERSON>", "faction": "Paragons", "art_url": "/cards/paragons/ranger.png", "n": 3, "e": 6, "s": 4, "w": 7, "rarity": "R"}, {"slug": "paragon-mystic", "name": "Mystic", "faction": "Paragons", "art_url": "/cards/paragons/mystic.png", "n": 6, "e": 4, "s": 2, "w": 8, "rarity": "R"}], "decks": [{"slug": "starter-ironwall", "name": "Ironwall", "description": "Defensive orientation; strong N/W.", "thumbnail_url": "/decks/ironwall.png", "cards": ["paragon-sentinel", "paragon-vanguard", "paragon-ranger", "paragon-mystic", "paragon-archon"]}, {"slug": "starter-glasscannon", "name": "Glass Cannon", "description": "High E/S values; fragile elsewhere.", "thumbnail_url": "/decks/glass.png", "cards": ["paragon-archon", "paragon-shade", "paragon-vanguard", "paragon-ranger", "paragon-mystic"]}, {"slug": "starter-bruteforce", "name": "Brute Force", "description": "Top/bottom smashers.", "thumbnail_url": "/decks/brute.png", "cards": ["paragon-colossus", "paragon-sentinel", "paragon-vanguard", "paragon-ranger", "paragon-mystic"]}]}, "assets": {"notes": "Replace art_url paths with your Paragons assets. Add more cards freely; stats are dummy placeholders for balancing later.", "placeholders": {"card_art_missing": "/cards/placeholder.png", "deck_thumbnail_missing": "/decks/placeholder.png"}}, "admin": {"importer": {"csvExample": "slug,name,faction,art_url,n,e,s,w,rarity\nparagon-sentinel,Sentinel,Paragons,/cards/paragons/sentinel.png,7,3,5,6,R", "ui": "Simple /admin/import to upload CSV → upsert into cards; map deck slug → card slugs for deck_cards."}}, "telemetry": {"events": ["user_signup", "match_created", "match_joined", "match_started", "move_made", "match_finished", "stats_updated"]}, "housekeeping": {"rateLimits": {"move": {"windowSec": 2, "max": 1}}, "timeouts": {"lobby_idle_minutes": 10, "turn_seconds": 60}, "cleanupJobs": ["cancel matches waiting > 30 min", "auto-finish idle active matches > 24h"]}, "notesForDev": ["For PvP join code, generate random A-Z0-9; ensure uniqueness via DB constraint.", "Use Supabase Realtime on matches row to push board_state/turn updates.", "Bo<PERSON> turn triggers server-side after player move (API route /api/bot/move).", "Prevent simultaneous moves by checking current_turn_user_id + row version.", "Deck selection fixed to prebuilt; no user deck editing in <PERSON>."]}