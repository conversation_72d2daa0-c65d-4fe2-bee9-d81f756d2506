import { Card } from '@/lib/types'
import Image from 'next/image'

interface CardMiniProps {
  card: Card
  size?: 'small' | 'medium' | 'large'
  showStats?: boolean
  className?: string
  onClick?: () => void
}

export default function CardMini({ 
  card, 
  size = 'medium', 
  showStats = true, 
  className = '',
  onClick 
}: CardMiniProps) {
  const sizeClasses = {
    small: 'w-16 h-20',
    medium: 'w-20 h-28',
    large: 'w-24 h-32'
  }

  const statSizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  }

  const getRarityColor = (rarity?: string) => {
    switch (rarity) {
      case 'C': return 'border-gray-400'
      case 'R': return 'border-blue-400'
      case 'SR': return 'border-purple-400'
      case 'SSR': return 'border-yellow-400'
      default: return 'border-gray-400'
    }
  }

  const getRarityBg = (rarity?: string) => {
    switch (rarity) {
      case 'C': return 'bg-gray-600'
      case 'R': return 'bg-blue-600'
      case 'SR': return 'bg-purple-600'
      case 'SSR': return 'bg-yellow-600'
      default: return 'bg-gray-600'
    }
  }

  return (
    <div 
      className={`
        relative ${sizeClasses[size]} 
        border-2 ${getRarityColor(card.rarity)} 
        rounded-lg overflow-hidden 
        bg-gray-800 
        ${onClick ? 'cursor-pointer hover:scale-105 transition-transform' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {/* Card Art */}
      <div className="relative w-full h-full">
        {/* Card name as placeholder */}
        <div className="absolute inset-0 flex items-center justify-center bg-gray-700">
          <span className="text-gray-400 text-xs text-center px-1">
            {card.name}
          </span>
        </div>

        {/* Rarity indicator */}
        {card.rarity && (
          <div className={`
            absolute top-1 right-1 
            ${getRarityBg(card.rarity)} 
            text-white text-xs px-1 rounded
          `}>
            {card.rarity}
          </div>
        )}

        {/* Card Stats */}
        {showStats && (
          <>
            {/* North */}
            <div className={`
              absolute top-0 left-1/2 transform -translate-x-1/2 
              bg-black bg-opacity-75 text-white 
              ${statSizeClasses[size]} font-bold 
              px-1 rounded-b
            `}>
              {card.n}
            </div>

            {/* East */}
            <div className={`
              absolute right-0 top-1/2 transform -translate-y-1/2 
              bg-black bg-opacity-75 text-white 
              ${statSizeClasses[size]} font-bold 
              px-1 rounded-l
            `}>
              {card.e}
            </div>

            {/* South */}
            <div className={`
              absolute bottom-0 left-1/2 transform -translate-x-1/2 
              bg-black bg-opacity-75 text-white 
              ${statSizeClasses[size]} font-bold 
              px-1 rounded-t
            `}>
              {card.s}
            </div>

            {/* West */}
            <div className={`
              absolute left-0 top-1/2 transform -translate-y-1/2 
              bg-black bg-opacity-75 text-white 
              ${statSizeClasses[size]} font-bold 
              px-1 rounded-r
            `}>
              {card.w}
            </div>
          </>
        )}
      </div>

      {/* Card Name (optional overlay) */}
      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 text-center">
        {card.name}
      </div>
    </div>
  )
}
