import { validate<PERSON>ove, executeMove, calculateCaptures, generateJoinCode } from '../lib/game-logic'
import { Card, BoardCell } from '../lib/types'

// Mock card data
const mockCard: Card = {
  id: 'test-card-1',
  slug: 'test-card',
  name: 'Test Card',
  n: 5,
  e: 3,
  s: 7,
  w: 2,
  faction: 'Test',
  art_url: '/test.png',
  rarity: 'C'
}

const mockCard2: Card = {
  id: 'test-card-2',
  slug: 'test-card-2',
  name: 'Test Card 2',
  n: 4,
  e: 6,
  s: 2,
  w: 8,
  faction: 'Test',
  art_url: '/test2.png',
  rarity: 'R'
}

const allCards = [mockCard, mockCard2]

describe('Game Logic', () => {
  describe('validateMove', () => {
    it('should validate a legal move', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      const result = validateMove(
        board,
        0,
        'test-card-1',
        'user-1',
        ['test-card-1'],
        'user-1'
      )
      
      expect(result.valid).toBe(true)
    })

    it('should reject move when not player turn', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      const result = validateMove(
        board,
        0,
        'test-card-1',
        'user-1',
        ['test-card-1'],
        'user-2'
      )
      
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Not your turn')
    })

    it('should reject move on occupied cell', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      board[0] = { owner: 'A', card_id: 'existing-card' }
      
      const result = validateMove(
        board,
        0,
        'test-card-1',
        'user-1',
        ['test-card-1'],
        'user-1'
      )
      
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Cell is already occupied')
    })

    it('should reject move with card not in hand', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      const result = validateMove(
        board,
        0,
        'test-card-1',
        'user-1',
        ['other-card'],
        'user-1'
      )
      
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Card not in hand')
    })
  })

  describe('calculateCaptures', () => {
    it('should calculate captures correctly', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      // Place opponent card at position 1 (to the right of position 0)
      board[1] = { owner: 'B', card_id: 'test-card-2' }
      
      const captures = calculateCaptures(board, 0, mockCard, 'A', allCards)
      
      // mockCard has E=3, mockCard2 has W=8
      // 3 < 8, so no capture should occur
      expect(captures).toEqual([])
    })

    it('should capture adjacent card with higher value', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      // Place opponent card at position 1
      board[1] = { owner: 'B', card_id: 'test-card-2' }
      
      // Create a card with higher E value
      const strongCard: Card = {
        ...mockCard,
        e: 9 // Higher than mockCard2's W=8
      }
      
      const captures = calculateCaptures(board, 0, strongCard, 'A', allCards)
      
      expect(captures).toContain(1)
    })
  })

  describe('executeMove', () => {
    it('should place card and update board', () => {
      const board: (BoardCell | null)[] = Array(9).fill(null)
      
      const result = executeMove(board, 0, mockCard, 'A', allCards)
      
      expect(result.valid).toBe(true)
      expect(result.newBoard[0]).toEqual({
        owner: 'A',
        card_id: mockCard.id
      })
      expect(result.gameOver).toBe(false)
    })

    it('should detect game over when board is full', () => {
      const board: (BoardCell | null)[] = Array(9).fill({ owner: 'A', card_id: 'some-card' })
      board[8] = null // Leave last cell empty
      
      const result = executeMove(board, 8, mockCard, 'B', allCards)
      
      expect(result.gameOver).toBe(true)
      expect(result.winner).toBe('A') // A has 8 cards, B has 1
    })
  })

  describe('generateJoinCode', () => {
    it('should generate 6-character code', () => {
      const code = generateJoinCode()
      expect(code).toHaveLength(6)
      expect(/^[A-Z0-9]+$/.test(code)).toBe(true)
    })

    it('should generate unique codes', () => {
      const codes = new Set()
      for (let i = 0; i < 100; i++) {
        codes.add(generateJoinCode())
      }
      // Should have close to 100 unique codes (very unlikely to have duplicates)
      expect(codes.size).toBeGreaterThan(95)
    })
  })
})
