-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  username TEXT UNIQUE NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create stats table
CREATE TABLE stats (
  user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  wins INTEGER DEFAULT 0,
  losses INTEGER DEFAULT 0,
  draws INTEGER DEFAULT 0,
  most_used_deck_id UUID,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cards table
CREATE TABLE cards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  faction TEXT,
  art_url TEXT,
  n INTEGER CHECK (n >= 0 AND n <= 10),
  e INTEGER CHECK (e >= 0 AND e <= 10),
  s INTEGER CHECK (s >= 0 AND s <= 10),
  w INTEGER CHECK (w >= 0 AND w <= 10),
  rarity TEXT,
  ability_key TEXT,
  meta JSONB
);

-- Create decks table
CREATE TABLE decks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  thumbnail_url TEXT
);

-- Create deck_cards junction table
CREATE TABLE deck_cards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deck_id UUID NOT NULL REFERENCES decks(id) ON DELETE CASCADE,
  card_id UUID NOT NULL REFERENCES cards(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  UNIQUE(deck_id, card_id)
);

-- Create matches table
CREATE TABLE matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'finished', 'cancelled')),
  mode TEXT DEFAULT 'pvp' CHECK (mode IN ('pvp', 'bot')),
  join_code TEXT UNIQUE,
  board_state JSONB DEFAULT '{"cells":[null,null,null,null,null,null,null,null,null]}',
  rules JSONB,
  current_turn_user_id UUID,
  winner_user_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create match_players table
CREATE TABLE match_players (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  is_host BOOLEAN DEFAULT FALSE,
  deck_id UUID REFERENCES decks(id),
  hand JSONB,
  color TEXT CHECK (color IN ('A', 'B')),
  score INTEGER DEFAULT 0
);

-- Create moves table
CREATE TABLE moves (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
  user_id UUID,
  card_id UUID REFERENCES cards(id),
  cell_index INTEGER CHECK (cell_index >= 0 AND cell_index <= 8),
  captures JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX deck_cards_deck_id_idx ON deck_cards(deck_id);
CREATE INDEX match_players_match_id_idx ON match_players(match_id);
CREATE INDEX moves_match_id_idx ON moves(match_id);
CREATE INDEX matches_join_code_idx ON matches(join_code);
CREATE INDEX matches_status_idx ON matches(status);

-- Add foreign key constraint for most_used_deck_id
ALTER TABLE stats ADD CONSTRAINT stats_most_used_deck_id_fkey 
  FOREIGN KEY (most_used_deck_id) REFERENCES decks(id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_matches_updated_at 
  BEFORE UPDATE ON matches 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stats_updated_at
  BEFORE UPDATE ON stats
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to increment stats
CREATE OR REPLACE FUNCTION increment_stats(
  user_id UUID,
  wins_inc INTEGER DEFAULT 0,
  losses_inc INTEGER DEFAULT 0,
  draws_inc INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO stats (user_id, wins, losses, draws)
  VALUES (user_id, wins_inc, losses_inc, draws_inc)
  ON CONFLICT (user_id)
  DO UPDATE SET
    wins = stats.wins + wins_inc,
    losses = stats.losses + losses_inc,
    draws = stats.draws + draws_inc,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
