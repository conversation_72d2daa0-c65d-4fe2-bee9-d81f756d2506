'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createClientComponentClient } from '@/lib/supabase'
import { AuthClient } from '@/lib/auth'
import { GameMatch, MatchPlayer, Card } from '@/lib/types'
import Board3x3 from '@/components/Board3x3'
import HandPanel from '@/components/HandPanel'

export default function MatchPage() {
  const [match, setMatch] = useState<GameMatch | null>(null)
  const [players, setPlayers] = useState<MatchPlayer[]>([])
  const [cards, setCards] = useState<Card[]>([])
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [making_move, setMakingMove] = useState(false)

  const router = useRouter()
  const params = useParams()
  const matchId = params.id as string
  
  const supabase = createClientComponentClient()
  const authClient = new AuthClient()

  useEffect(() => {
    loadMatchData()
    
    // Set up real-time subscriptions
    const matchSubscription = supabase
      .channel(`match-${matchId}`)
      .on('postgres_changes', 
        { event: 'UPDATE', schema: 'public', table: 'matches', filter: `id=eq.${matchId}` },
        (payload) => {
          setMatch(payload.new as GameMatch)
          
          // Trigger bot move if it's bot's turn
          if (payload.new.mode === 'bot' && 
              payload.new.status === 'active' && 
              payload.new.current_turn_user_id === null) {
            setTimeout(() => triggerBotMove(), 1000) // Small delay for better UX
          }
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_players', filter: `match_id=eq.${matchId}` },
        () => {
          loadPlayers()
        }
      )
      .subscribe()

    return () => {
      matchSubscription.unsubscribe()
    }
  }, [matchId])

  const loadMatchData = async () => {
    try {
      // Get current user
      const { user } = await authClient.getCurrentUser()
      if (!user) {
        router.push('/auth')
        return
      }
      setCurrentUser(user)

      // Load match
      const { data: matchData, error: matchError } = await supabase
        .from('matches')
        .select('*')
        .eq('id', matchId)
        .single()

      if (matchError || !matchData) {
        setError('Match not found')
        return
      }

      setMatch(matchData)

      // Load players
      await loadPlayers()

      // Load all cards
      const { data: cardsData, error: cardsError } = await supabase
        .from('cards')
        .select('*')

      if (cardsError) {
        setError('Failed to load cards')
        return
      }

      setCards(cardsData || [])

    } catch (err) {
      console.error('Error loading match:', err)
      setError('Failed to load match')
    } finally {
      setLoading(false)
    }
  }

  const loadPlayers = async () => {
    const { data: playersData, error } = await supabase
      .from('match_players')
      .select(`
        *,
        profiles (username, avatar_url)
      `)
      .eq('match_id', matchId)

    if (!error && playersData) {
      setPlayers(playersData)
    }
  }

  const triggerBotMove = async () => {
    try {
      await fetch('/api/bot/move', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ match_id: matchId }),
      })
    } catch (error) {
      console.error('Error triggering bot move:', error)
    }
  }

  const makeMove = async (cellIndex: number) => {
    if (!selectedCardId || !currentUser || making_move) return

    setMakingMove(true)
    setError('')

    try {
      const response = await fetch('/api/move', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          match_id: matchId,
          card_id: selectedCardId,
          cell_index: cellIndex
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to make move')
      }

      // Clear selected card
      setSelectedCardId(null)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to make move')
    } finally {
      setMakingMove(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading match...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-lg mb-4">{error}</div>
          <a
            href="/play"
            className="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Play
          </a>
        </div>
      </div>
    )
  }

  const currentPlayer = players.find(p => p.user_id === currentUser?.id)
  const opponentPlayer = players.find(p => p.user_id !== currentUser?.id)
  const isMyTurn = match?.current_turn_user_id === currentUser?.id
  const legalCells = selectedCardId ? 
    match?.board_state.cells.map((cell, index) => cell === null ? index : -1).filter(i => i !== -1) || []
    : []

  // Game over state
  if (match?.status === 'finished') {
    const winner = match.winner_user_id === currentUser?.id ? 'You' : 
                   match.winner_user_id === null ? 'Draw' : 'Opponent'
    
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            Game Over
          </h1>
          <div className={`text-2xl mb-8 ${
            winner === 'You' ? 'text-green-400' : 
            winner === 'Draw' ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {winner === 'Draw' ? 'It\'s a draw!' : `${winner} won!`}
          </div>
          
          <div className="space-x-4">
            <a
              href="/play"
              className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
            >
              Play Again
            </a>
            <a
              href="/"
              className="inline-flex items-center px-6 py-3 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 font-medium rounded-lg transition-colors"
            >
              Home
            </a>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Paragons: Triad Battle
          </h1>
          <div className="text-gray-300">
            {match?.mode === 'pvp' ? 'Player vs Player' : 'Player vs Bot'}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-center">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Opponent Info */}
          <div className="lg:order-1">
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-2">Opponent</h3>
              <div className="flex items-center space-x-3">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold
                  ${opponentPlayer?.color === 'A' ? 'bg-blue-600' : 'bg-red-600'}
                `}>
                  {opponentPlayer?.color}
                </div>
                <div>
                  <div className="text-white">
                    {opponentPlayer?.user_id ? (opponentPlayer as any).profiles?.username || 'Player' : 'Bot'}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {(opponentPlayer?.hand as string[])?.length || 0} cards
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Game Board */}
          <div className="lg:order-2">
            <Board3x3
              boardState={match?.board_state.cells || []}
              cards={cards}
              onCellClick={makeMove}
              isMyTurn={isMyTurn && !making_move}
              legalCells={legalCells}
              playerColor={currentPlayer?.color || 'A'}
            />
          </div>

          {/* Player Hand */}
          <div className="lg:order-3">
            <HandPanel
              hand={(currentPlayer?.hand as string[]) || []}
              cards={cards}
              selectedCardId={selectedCardId}
              onCardSelect={setSelectedCardId}
              isMyTurn={isMyTurn}
              disabled={making_move}
            />
          </div>
        </div>

        {/* Back Button */}
        <div className="text-center mt-6">
          <a
            href="/play"
            className="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Play
          </a>
        </div>
      </div>
    </div>
  )
}
