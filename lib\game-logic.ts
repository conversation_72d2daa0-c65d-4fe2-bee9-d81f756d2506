import { Card, BoardCell, GameMatch, MatchPlayer } from './types'

export interface GameState {
  board: (BoardCell | null)[]
  players: MatchPlayer[]
  currentTurn: 'A' | 'B'
  isGameOver: boolean
  winner: 'A' | 'B' | 'draw' | null
}

export interface MoveResult {
  valid: boolean
  captures: number[]
  newBoard: (BoardCell | null)[]
  gameOver: boolean
  winner: 'A' | 'B' | 'draw' | null
  error?: string
}

/**
 * Validates if a move is legal
 */
export function validateMove(
  board: (BoardCell | null)[],
  cellIndex: number,
  cardId: string,
  playerId: string,
  playerHand: string[],
  currentTurnUserId: string
): { valid: boolean; error?: string } {
  // Check if it's the player's turn
  if (playerId !== currentTurnUserId) {
    return { valid: false, error: "Not your turn" }
  }

  // Check if cell index is valid
  if (cellIndex < 0 || cellIndex > 8) {
    return { valid: false, error: "Invalid cell index" }
  }

  // Check if cell is empty
  if (board[cellIndex] !== null) {
    return { valid: false, error: "Cell is already occupied" }
  }

  // Check if card is in player's hand
  if (!playerHand.includes(cardId)) {
    return { valid: false, error: "Card not in hand" }
  }

  return { valid: true }
}

/**
 * Calculates which adjacent cards are captured by a placement
 */
export function calculateCaptures(
  board: (BoardCell | null)[],
  cellIndex: number,
  placedCard: Card,
  playerColor: 'A' | 'B',
  allCards: Card[]
): number[] {
  const captures: number[] = []
  const cardMap = new Map(allCards.map(card => [card.id, card]))

  // Define adjacent cells for each position
  const adjacentCells = getAdjacentCells(cellIndex)

  // Check each adjacent cell
  for (const { index: adjIndex, direction } of adjacentCells) {
    const adjCell = board[adjIndex]
    
    // Skip if adjacent cell is empty or owned by same player
    if (!adjCell || adjCell.owner === playerColor) {
      continue
    }

    const adjCard = cardMap.get(adjCell.card_id!)
    if (!adjCard) continue

    // Get the values for comparison
    const placedValue = getCardValue(placedCard, direction)
    const adjValue = getCardValue(adjCard, getOppositeDirection(direction))

    // Capture if placed card's value is higher
    if (placedValue > adjValue) {
      captures.push(adjIndex)
    }
  }

  return captures
}

/**
 * Executes a move and returns the result
 */
export function executeMove(
  board: (BoardCell | null)[],
  cellIndex: number,
  card: Card,
  playerColor: 'A' | 'B',
  allCards: Card[]
): MoveResult {
  // Create new board state
  const newBoard = [...board]
  
  // Place the card
  newBoard[cellIndex] = {
    owner: playerColor,
    card_id: card.id
  }

  // Calculate captures
  const captures = calculateCaptures(board, cellIndex, card, playerColor, allCards)

  // Apply captures
  for (const captureIndex of captures) {
    if (newBoard[captureIndex]) {
      newBoard[captureIndex]!.owner = playerColor
    }
  }

  // Check if game is over (all 9 cells filled)
  const gameOver = newBoard.every(cell => cell !== null)
  let winner: 'A' | 'B' | 'draw' | null = null

  if (gameOver) {
    const scoreA = newBoard.filter(cell => cell?.owner === 'A').length
    const scoreB = newBoard.filter(cell => cell?.owner === 'B').length
    
    if (scoreA > scoreB) {
      winner = 'A'
    } else if (scoreB > scoreA) {
      winner = 'B'
    } else {
      winner = 'draw'
    }
  }

  return {
    valid: true,
    captures,
    newBoard,
    gameOver,
    winner
  }
}

/**
 * Gets adjacent cell indices and their directions for a given cell
 */
function getAdjacentCells(cellIndex: number): Array<{ index: number; direction: 'n' | 'e' | 's' | 'w' }> {
  const row = Math.floor(cellIndex / 3)
  const col = cellIndex % 3
  const adjacent: Array<{ index: number; direction: 'n' | 'e' | 's' | 'w' }> = []

  // North
  if (row > 0) {
    adjacent.push({ index: cellIndex - 3, direction: 'n' })
  }
  
  // East
  if (col < 2) {
    adjacent.push({ index: cellIndex + 1, direction: 'e' })
  }
  
  // South
  if (row < 2) {
    adjacent.push({ index: cellIndex + 3, direction: 's' })
  }
  
  // West
  if (col > 0) {
    adjacent.push({ index: cellIndex - 1, direction: 'w' })
  }

  return adjacent
}

/**
 * Gets the card value for a specific direction
 */
function getCardValue(card: Card, direction: 'n' | 'e' | 's' | 'w'): number {
  switch (direction) {
    case 'n': return card.n
    case 'e': return card.e
    case 's': return card.s
    case 'w': return card.w
    default: return 0
  }
}

/**
 * Gets the opposite direction
 */
function getOppositeDirection(direction: 'n' | 'e' | 's' | 'w'): 'n' | 'e' | 's' | 'w' {
  switch (direction) {
    case 'n': return 's'
    case 'e': return 'w'
    case 's': return 'n'
    case 'w': return 'e'
  }
}

/**
 * Generates a random 6-character join code
 */
export function generateJoinCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Shuffles an array and returns a new array
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * Creates a hand of 5 cards from a deck
 */
export function createHand(deckCards: Card[]): string[] {
  const shuffled = shuffleArray(deckCards)
  return shuffled.slice(0, 5).map(card => card.id)
}
