-- Insert seed cards
INSERT INTO cards (slug, name, faction, art_url, n, e, s, w, rarity, meta) VALUES
  ('paragon-sentinel', 'Sentinel', 'Paragons', '/cards/paragons/sentinel.png', 7, 3, 5, 6, 'R', '{"blurb": "Solid north/west wall."}'),
  ('paragon-archon', 'Archon', 'Paragons', '/cards/paragons/archon.png', 4, 8, 6, 2, 'SR', '{"blurb": "Right-side powerhouse."}'),
  ('paragon-vanguard', 'Vanguard', 'Paragons', '/cards/paragons/vanguard.png', 5, 5, 5, 5, 'C', '{"blurb": "Balanced."}'),
  ('paragon-shade', 'Shade', 'Paragons', '/cards/paragons/shade.png', 2, 7, 7, 3, 'R', '{}'),
  ('paragon-colossus', 'Colossus', 'Paragons', '/cards/paragons/colossus.png', 8, 2, 8, 1, 'SR', '{}'),
  ('paragon-ranger', 'Ranger', 'Paragons', '/cards/paragons/ranger.png', 3, 6, 4, 7, 'R', '{}'),
  ('paragon-mystic', 'Mystic', 'Paragons', '/cards/paragons/mystic.png', 6, 4, 2, 8, 'R', '{}');

-- Insert seed decks
INSERT INTO decks (slug, name, description, thumbnail_url) VALUES
  ('starter-ironwall', 'Ironwall', 'Defensive orientation; strong N/W.', '/decks/ironwall.png'),
  ('starter-glasscannon', 'Glass Cannon', 'High E/S values; fragile elsewhere.', '/decks/glass.png'),
  ('starter-bruteforce', 'Brute Force', 'Top/bottom smashers.', '/decks/brute.png');

-- Insert deck-card relationships
-- Ironwall deck
INSERT INTO deck_cards (deck_id, card_id, quantity) 
SELECT 
  (SELECT id FROM decks WHERE slug = 'starter-ironwall'),
  cards.id,
  1
FROM cards 
WHERE cards.slug IN ('paragon-sentinel', 'paragon-vanguard', 'paragon-ranger', 'paragon-mystic', 'paragon-archon');

-- Glass Cannon deck
INSERT INTO deck_cards (deck_id, card_id, quantity) 
SELECT 
  (SELECT id FROM decks WHERE slug = 'starter-glasscannon'),
  cards.id,
  1
FROM cards 
WHERE cards.slug IN ('paragon-archon', 'paragon-shade', 'paragon-vanguard', 'paragon-ranger', 'paragon-mystic');

-- Brute Force deck
INSERT INTO deck_cards (deck_id, card_id, quantity) 
SELECT 
  (SELECT id FROM decks WHERE slug = 'starter-bruteforce'),
  cards.id,
  1
FROM cards 
WHERE cards.slug IN ('paragon-colossus', 'paragon-sentinel', 'paragon-vanguard', 'paragon-ranger', 'paragon-mystic');
