import { createServerComponentClient } from '@/lib/supabase'
import { generateJoinCode } from '@/lib/game-logic'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { mode } = body

    if (!mode || !['pvp', 'bot'].includes(mode)) {
      return NextResponse.json({ error: 'Invalid mode' }, { status: 400 })
    }

    let joinCode: string | null = null
    
    // Generate join code for PvP matches
    if (mode === 'pvp') {
      let attempts = 0
      const maxAttempts = 5

      while (attempts < maxAttempts) {
        const candidateCode = generateJoinCode()
        
        // Check if code already exists
        const { data: existingMatch } = await supabase
          .from('matches')
          .select('id')
          .eq('join_code', candidateCode)
          .single()

        if (!existingMatch) {
          joinCode = candidateCode
          break
        }
        
        attempts++
      }

      if (!joinCode) {
        return NextResponse.json({ error: 'Failed to generate unique join code' }, { status: 500 })
      }
    }

    // Create the match
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .insert({
        status: 'waiting',
        mode,
        join_code: joinCode,
        board_state: { cells: [null, null, null, null, null, null, null, null, null] },
        rules: { basic: true, same: false, plus: false, combo: false, reverse: false, fallenAce: false }
      })
      .select()
      .single()

    if (matchError) {
      console.error('Error creating match:', matchError)
      return NextResponse.json({ error: 'Failed to create match' }, { status: 500 })
    }

    // Add the host player
    const { error: playerError } = await supabase
      .from('match_players')
      .insert({
        match_id: match.id,
        user_id: user.id,
        is_host: true,
        color: 'A',
        score: 0
      })

    if (playerError) {
      console.error('Error adding host player:', playerError)
      
      // Clean up the match if player creation failed
      await supabase.from('matches').delete().eq('id', match.id)
      
      return NextResponse.json({ error: 'Failed to create match' }, { status: 500 })
    }

    return NextResponse.json({
      match_id: match.id,
      join_code: joinCode
    })

  } catch (error) {
    console.error('Error in match creation:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
