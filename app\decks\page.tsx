import { createServerComponentClient } from '@/lib/supabase'
import { Deck, Card } from '@/lib/types'
import CardMini from '@/components/CardMini'
import Image from 'next/image'

export default async function DecksPage() {
  const supabase = await createServerComponentClient()

  // Load decks with their cards
  const { data: decks, error: decksError } = await supabase
    .from('decks')
    .select('*')
    .order('name')

  if (decksError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-red-400 text-center">
            Failed to load decks: {decksError.message}
          </div>
        </div>
      </div>
    )
  }

  // Load cards for each deck
  const decksWithCards = await Promise.all(
    (decks || []).map(async (deck) => {
      const { data: deckCards } = await supabase
        .from('deck_cards')
        .select(`
          quantity,
          cards (*)
        `)
        .eq('deck_id', deck.id)

      return {
        ...deck,
        cards: deckCards?.map((dc: any) => dc.cards) || []
      }
    })
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Deck Gallery
          </h1>
          <p className="text-gray-300 text-lg">
            Explore the prebuilt decks available in Paragons: Triad
          </p>
        </div>

        {/* Decks Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {decksWithCards.map((deck) => (
            <div
              key={deck.id}
              className="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-purple-500 transition-colors"
            >
              {/* Deck Header */}
              <div className="flex items-center space-x-4 mb-6">
                {deck.thumbnail_url ? (
                  <Image
                    src={deck.thumbnail_url}
                    alt={deck.name}
                    width={64}
                    height={64}
                    className="rounded-lg"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = '/decks/placeholder.png'
                    }}
                  />
                ) : (
                  <div className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-sm">DECK</span>
                  </div>
                )}

                <div>
                  <h3 className="text-xl font-semibold text-white">
                    {deck.name}
                  </h3>
                  <p className="text-gray-400 text-sm">
                    {deck.description}
                  </p>
                  <p className="text-gray-500 text-xs mt-1">
                    {deck.cards.length} cards
                  </p>
                </div>
              </div>

              {/* Cards Grid */}
              <div className="grid grid-cols-5 gap-2">
                {deck.cards.map((card: Card, index: number) => (
                  <CardMini
                    key={`${card.id}-${index}`}
                    card={card}
                    size="small"
                    showStats={true}
                  />
                ))}
              </div>

              {/* Deck Stats */}
              <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-white font-semibold">
                      {Math.round(deck.cards.reduce((sum: number, card: Card) => sum + card.n, 0) / deck.cards.length)}
                    </div>
                    <div className="text-gray-400 text-xs">Avg N</div>
                  </div>
                  <div>
                    <div className="text-white font-semibold">
                      {Math.round(deck.cards.reduce((sum: number, card: Card) => sum + card.e, 0) / deck.cards.length)}
                    </div>
                    <div className="text-gray-400 text-xs">Avg E</div>
                  </div>
                  <div>
                    <div className="text-white font-semibold">
                      {Math.round(deck.cards.reduce((sum: number, card: Card) => sum + card.s, 0) / deck.cards.length)}
                    </div>
                    <div className="text-gray-400 text-xs">Avg S</div>
                  </div>
                  <div>
                    <div className="text-white font-semibold">
                      {Math.round(deck.cards.reduce((sum: number, card: Card) => sum + card.w, 0) / deck.cards.length)}
                    </div>
                    <div className="text-gray-400 text-xs">Avg W</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Back to Home */}
        <div className="text-center mt-12">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors"
          >
            Back to Home
          </a>
        </div>
      </div>
    </div>
  )
}
