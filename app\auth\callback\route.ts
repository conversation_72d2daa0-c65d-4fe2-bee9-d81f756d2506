import { createServerComponentClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const supabase = await createServerComponentClient()
    
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error && data.user) {
      // Check if user has a profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single()

      // If no profile exists, redirect to profile setup
      if (!profile) {
        return NextResponse.redirect(`${requestUrl.origin}/auth/setup`)
      }

      // Redirect to home page
      return NextResponse.redirect(`${requestUrl.origin}/`)
    }
  }

  // If there was an error, redirect to auth page with error
  return NextResponse.redirect(`${requestUrl.origin}/auth?error=auth_failed`)
}
