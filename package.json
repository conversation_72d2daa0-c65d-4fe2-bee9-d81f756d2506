{"name": "paragons-triad", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.2", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}