# Deployment Guide

This guide will help you deploy Paragons: Triad to production.

## Prerequisites

1. **Supabase Project**: Create a new project at [supabase.com](https://supabase.com)
2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com) (recommended)
3. **GitHub Repository**: Push your code to GitHub

## Step 1: Set Up Supabase

### 1.1 Create Project
1. Go to [supabase.com](https://supabase.com) and create a new project
2. Choose a project name and database password
3. Wait for the project to be created

### 1.2 Run Database Migrations
1. Go to the SQL Editor in your Supabase dashboard
2. Run each migration file in order:
   - Copy and paste the contents of `supabase/migrations/001_initial_schema.sql`
   - Run the query
   - Repeat for `002_rls_policies.sql` and `003_seed_data.sql`

### 1.3 Get API Keys
1. Go to Settings > API in your Supabase dashboard
2. Copy your Project URL and anon/public key
3. Keep these safe - you'll need them for deployment

### 1.4 Enable Realtime (Optional)
1. Go to Database > Replication
2. Enable realtime for the `matches` table
3. Enable realtime for the `moves` table

## Step 2: Deploy to Vercel

### 2.1 Connect Repository
1. Go to [vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your GitHub repository
4. Vercel will automatically detect it's a Next.js project

### 2.2 Configure Environment Variables
1. In the deployment settings, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```
2. Use the values from Step 1.3

### 2.3 Deploy
1. Click "Deploy"
2. Wait for the build to complete
3. Your app will be available at the provided URL

## Step 3: Add Card Art

### 3.1 Prepare Images
1. Collect your Paragons Age of Champions card art
2. Resize images to recommended sizes:
   - Cards: 200x280px PNG
   - Deck thumbnails: 64x64px PNG

### 3.2 Upload to Public Folder
1. Add card images to `public/cards/paragons/`
2. Add deck thumbnails to `public/decks/`
3. Follow the naming convention from the seed data

### 3.3 Update Database
1. Update the `art_url` fields in the `cards` table
2. Update the `thumbnail_url` fields in the `decks` table
3. Use paths like `/cards/paragons/sentinel.png`

## Step 4: Custom Domain (Optional)

### 4.1 Add Domain in Vercel
1. Go to your project settings in Vercel
2. Click "Domains"
3. Add your custom domain

### 4.2 Configure DNS
1. Add the DNS records provided by Vercel
2. Wait for DNS propagation (can take up to 24 hours)

## Step 5: Monitoring and Maintenance

### 5.1 Monitor Performance
- Use Vercel Analytics to track performance
- Monitor Supabase usage in the dashboard
- Set up alerts for errors

### 5.2 Database Maintenance
- Regularly check for abandoned matches
- Clean up old match data if needed
- Monitor database size and performance

### 5.3 Updates
- Test changes locally first
- Use Vercel's preview deployments for testing
- Deploy to production after testing

## Troubleshooting

### Common Issues

**Build Fails**
- Check that all environment variables are set
- Ensure TypeScript types are correct
- Check the build logs for specific errors

**Database Connection Issues**
- Verify Supabase URL and keys are correct
- Check that RLS policies are properly configured
- Ensure database migrations ran successfully

**Real-time Not Working**
- Verify realtime is enabled for the correct tables
- Check browser console for WebSocket errors
- Ensure Supabase project is not paused

**Images Not Loading**
- Check that image paths are correct
- Ensure images are in the public folder
- Verify image file formats are supported

### Getting Help

1. Check the Vercel deployment logs
2. Check the Supabase logs
3. Review the browser console for errors
4. Check the GitHub issues for known problems

## Security Considerations

1. **Environment Variables**: Never commit API keys to version control
2. **RLS Policies**: Ensure Row Level Security is properly configured
3. **Rate Limiting**: Consider adding rate limiting for API routes
4. **CORS**: Configure CORS settings if needed

## Performance Optimization

1. **Image Optimization**: Use Next.js Image component for card art
2. **Caching**: Configure appropriate cache headers
3. **Database Indexing**: Ensure proper indexes are in place
4. **Bundle Size**: Monitor and optimize bundle size

## Backup Strategy

1. **Database**: Set up regular Supabase backups
2. **Code**: Keep code in version control
3. **Assets**: Backup card art and other assets
4. **Configuration**: Document all configuration settings
