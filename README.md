# Paragons: Triad

A lightweight Triple Triad inspired web game using Paragons: Age of Champions card art. Built with Next.js, TypeScript, TailwindCSS, and Supabase.

## Features

- **Strategic Card Gameplay**: Classic Triple Triad mechanics with N/E/S/W card values
- **Real-time Multiplayer**: PvP matches with join codes and live updates
- **Bot AI**: Practice against an intelligent AI opponent
- **Prebuilt Decks**: Curated deck combinations for balanced gameplay
- **User Profiles**: Track wins, losses, and statistics
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, TailwindCSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL + Auth + Realtime)
- **Hosting**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd paragons-triad
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Set up the database:
   - Create a new Supabase project
   - Run the SQL migrations in order:
     - `supabase/migrations/001_initial_schema.sql`
     - `supabase/migrations/002_rls_policies.sql`
     - `supabase/migrations/003_seed_data.sql`

5. Start the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Game Rules

### Basic Gameplay
1. Each player starts with 5 cards from their chosen deck
2. Players take turns placing cards on a 3x3 grid
3. When a card is placed, it compares its edge values with adjacent opponent cards
4. If the placed card's value is higher, it captures the adjacent card
5. The player controlling more cards when the grid is full wins

### Card Values
- Each card has four values: North (N), East (E), South (S), West (W)
- Values range from 0-10
- Higher values capture lower values on adjacent edges

### Game Modes
- **PvP**: Create a match and share the 6-character join code with a friend
- **Bot**: Practice against an AI that uses greedy capture strategy

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── decks/             # Deck gallery
│   ├── lobby/             # Match lobby
│   ├── match/             # Game interface
│   ├── play/              # Game mode selection
│   └── profile/           # User profiles
├── components/            # React components
├── lib/                   # Utilities and logic
│   ├── auth.ts           # Authentication helpers
│   ├── bot-ai.ts         # Bot AI implementation
│   ├── game-logic.ts     # Core game mechanics
│   ├── supabase.ts       # Database client
│   └── types.ts          # TypeScript definitions
├── supabase/             # Database migrations
└── public/               # Static assets
```

## Adding Card Art

1. Add card images to `public/cards/paragons/`
2. Add deck thumbnails to `public/decks/`
3. Update the database with correct `art_url` and `thumbnail_url` paths
4. Recommended image sizes:
   - Cards: 200x280px PNG
   - Deck thumbnails: 64x64px PNG

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Manual Deployment

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Acknowledgments

- Inspired by Final Fantasy VIII's Triple Triad card game
- Built for the Paragons Age of Champions community
- Card art and assets belong to their respective owners
