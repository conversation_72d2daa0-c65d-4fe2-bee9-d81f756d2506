import { createServerComponentClient } from '@/lib/supabase'
import { validateMove, executeMove } from '@/lib/game-logic'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { match_id, card_id, cell_index } = body

    if (!match_id || !card_id || cell_index === undefined) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
    }

    // Get match data
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .select('*')
      .eq('id', match_id)
      .single()

    if (matchError || !match) {
      return NextResponse.json({ error: 'Match not found' }, { status: 404 })
    }

    if (match.status !== 'active') {
      return NextResponse.json({ error: 'Match is not active' }, { status: 400 })
    }

    // Get player data
    const { data: player, error: playerError } = await supabase
      .from('match_players')
      .select('*')
      .eq('match_id', match_id)
      .eq('user_id', user.id)
      .single()

    if (playerError || !player) {
      return NextResponse.json({ error: 'Player not found in match' }, { status: 404 })
    }

    // Get the card
    const { data: card, error: cardError } = await supabase
      .from('cards')
      .select('*')
      .eq('id', card_id)
      .single()

    if (cardError || !card) {
      return NextResponse.json({ error: 'Card not found' }, { status: 404 })
    }

    // Get all cards for capture calculation
    const { data: allCards, error: allCardsError } = await supabase
      .from('cards')
      .select('*')

    if (allCardsError || !allCards) {
      return NextResponse.json({ error: 'Failed to load cards' }, { status: 500 })
    }

    // Validate the move
    const validation = validateMove(
      match.board_state.cells,
      cell_index,
      card_id,
      user.id,
      player.hand || [],
      match.current_turn_user_id
    )

    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    // Execute the move
    const moveResult = executeMove(
      match.board_state.cells,
      cell_index,
      card,
      player.color,
      allCards
    )

    // Update player's hand (remove the played card)
    const newHand = (player.hand || []).filter((handCardId: string) => handCardId !== card_id)

    // Update match board state
    const { error: matchUpdateError } = await supabase
      .from('matches')
      .update({
        board_state: { cells: moveResult.newBoard },
        current_turn_user_id: moveResult.gameOver ? null : (
          // Switch turns
          player.color === 'A' 
            ? await getPlayerByColor(supabase, match_id, 'B')
            : await getPlayerByColor(supabase, match_id, 'A')
        ),
        status: moveResult.gameOver ? 'finished' : 'active',
        winner_user_id: moveResult.gameOver && moveResult.winner !== 'draw' 
          ? await getPlayerByColor(supabase, match_id, moveResult.winner!)
          : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', match_id)

    if (matchUpdateError) {
      console.error('Error updating match:', matchUpdateError)
      return NextResponse.json({ error: 'Failed to update match' }, { status: 500 })
    }

    // Update player's hand and score
    const newScore = moveResult.newBoard.filter(cell => cell?.owner === player.color).length
    
    const { error: playerUpdateError } = await supabase
      .from('match_players')
      .update({
        hand: newHand,
        score: newScore
      })
      .eq('id', player.id)

    if (playerUpdateError) {
      console.error('Error updating player:', playerUpdateError)
      return NextResponse.json({ error: 'Failed to update player' }, { status: 500 })
    }

    // Record the move
    const { error: moveError } = await supabase
      .from('moves')
      .insert({
        match_id,
        user_id: user.id,
        card_id,
        cell_index,
        captures: moveResult.captures
      })

    if (moveError) {
      console.error('Error recording move:', moveError)
      // Don't fail the request for this
    }

    // Update stats if game is over
    if (moveResult.gameOver) {
      await updatePlayerStats(supabase, match_id, moveResult.winner)
    }

    return NextResponse.json({
      ok: true,
      captures: moveResult.captures,
      game_over: moveResult.gameOver,
      winner: moveResult.winner
    })

  } catch (error) {
    console.error('Error processing move:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper function to get player user_id by color
async function getPlayerByColor(supabase: any, matchId: string, color: 'A' | 'B'): Promise<string | null> {
  const { data: player } = await supabase
    .from('match_players')
    .select('user_id')
    .eq('match_id', matchId)
    .eq('color', color)
    .single()

  return player?.user_id || null
}

// Helper function to update player stats
async function updatePlayerStats(supabase: any, matchId: string, winner: 'A' | 'B' | 'draw' | null) {
  try {
    const { data: players } = await supabase
      .from('match_players')
      .select('user_id, color')
      .eq('match_id', matchId)
      .not('user_id', 'is', null) // Exclude bot players

    if (!players) return

    for (const player of players) {
      if (!player.user_id) continue

      let statUpdate: any = {}
      
      if (winner === 'draw') {
        statUpdate.draws = 1
      } else if (winner === player.color) {
        statUpdate.wins = 1
      } else {
        statUpdate.losses = 1
      }

      // Use raw SQL to increment stats
      await supabase.rpc('increment_stats', {
        user_id: player.user_id,
        wins_inc: statUpdate.wins || 0,
        losses_inc: statUpdate.losses || 0,
        draws_inc: statUpdate.draws || 0
      })
    }
  } catch (error) {
    console.error('Error updating stats:', error)
  }
}
