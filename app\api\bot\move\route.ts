import { createServerComponentClient } from '@/lib/supabase'
import { TriadBot } from '@/lib/bot-ai'
import { executeMove } from '@/lib/game-logic'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    const body = await request.json()
    const { match_id } = body

    if (!match_id) {
      return NextResponse.json({ error: 'Match ID is required' }, { status: 400 })
    }

    // Get match data
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .select('*')
      .eq('id', match_id)
      .single()

    if (matchError || !match) {
      return NextResponse.json({ error: 'Match not found' }, { status: 404 })
    }

    if (match.status !== 'active') {
      return NextResponse.json({ error: 'Match is not active' }, { status: 400 })
    }

    if (match.mode !== 'bot') {
      return NextResponse.json({ error: 'This endpoint is only for bot matches' }, { status: 400 })
    }

    // Get bot player
    const { data: botPlayer, error: botPlayerError } = await supabase
      .from('match_players')
      .select('*')
      .eq('match_id', match_id)
      .is('user_id', null)
      .single()

    if (botPlayerError || !botPlayer) {
      return NextResponse.json({ error: 'Bot player not found' }, { status: 404 })
    }

    // Check if it's bot's turn
    if (match.current_turn_user_id !== null) {
      return NextResponse.json({ error: 'Not bot\'s turn' }, { status: 400 })
    }

    // Get all cards
    const { data: allCards, error: allCardsError } = await supabase
      .from('cards')
      .select('*')

    if (allCardsError || !allCards) {
      return NextResponse.json({ error: 'Failed to load cards' }, { status: 500 })
    }

    // Calculate bot's move
    const bot = new TriadBot(allCards)
    const botMove = bot.calculateBestMove(
      match.board_state.cells,
      botPlayer.hand || [],
      botPlayer.color
    )

    if (!botMove) {
      return NextResponse.json({ error: 'Bot cannot make a move' }, { status: 500 })
    }

    // Get the card being played
    const card = allCards.find(c => c.id === botMove.cardId)
    if (!card) {
      return NextResponse.json({ error: 'Card not found' }, { status: 500 })
    }

    // Execute the move
    const moveResult = executeMove(
      match.board_state.cells,
      botMove.cellIndex,
      card,
      botPlayer.color,
      allCards
    )

    // Update bot's hand
    const newHand = (botPlayer.hand || []).filter((handCardId: string) => handCardId !== botMove.cardId)

    // Get human player for turn switching
    const { data: humanPlayer } = await supabase
      .from('match_players')
      .select('user_id')
      .eq('match_id', match_id)
      .not('user_id', 'is', null)
      .single()

    // Update match
    const { error: matchUpdateError } = await supabase
      .from('matches')
      .update({
        board_state: { cells: moveResult.newBoard },
        current_turn_user_id: moveResult.gameOver ? null : humanPlayer?.user_id,
        status: moveResult.gameOver ? 'finished' : 'active',
        winner_user_id: moveResult.gameOver && moveResult.winner !== 'draw' 
          ? (moveResult.winner === botPlayer.color ? null : humanPlayer?.user_id)
          : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', match_id)

    if (matchUpdateError) {
      console.error('Error updating match:', matchUpdateError)
      return NextResponse.json({ error: 'Failed to update match' }, { status: 500 })
    }

    // Update bot player
    const newScore = moveResult.newBoard.filter(cell => cell?.owner === botPlayer.color).length
    
    const { error: botUpdateError } = await supabase
      .from('match_players')
      .update({
        hand: newHand,
        score: newScore
      })
      .eq('id', botPlayer.id)

    if (botUpdateError) {
      console.error('Error updating bot player:', botUpdateError)
      return NextResponse.json({ error: 'Failed to update bot player' }, { status: 500 })
    }

    // Record the move
    const { error: moveError } = await supabase
      .from('moves')
      .insert({
        match_id,
        user_id: null, // Bot move
        card_id: botMove.cardId,
        cell_index: botMove.cellIndex,
        captures: moveResult.captures
      })

    if (moveError) {
      console.error('Error recording bot move:', moveError)
    }

    // Update stats if game is over
    if (moveResult.gameOver && humanPlayer?.user_id) {
      try {
        let statUpdate: any = {}
        
        if (moveResult.winner === 'draw') {
          statUpdate.draws = 1
        } else if (moveResult.winner === botPlayer.color) {
          statUpdate.losses = 1 // Human lost to bot
        } else {
          statUpdate.wins = 1 // Human beat bot
        }

        await supabase.rpc('increment_stats', {
          user_id: humanPlayer.user_id,
          wins_inc: statUpdate.wins || 0,
          losses_inc: statUpdate.losses || 0,
          draws_inc: statUpdate.draws || 0
        })
      } catch (error) {
        console.error('Error updating stats:', error)
      }
    }

    return NextResponse.json({
      ok: true,
      move: {
        card_id: botMove.cardId,
        cell_index: botMove.cellIndex,
        captures: moveResult.captures
      },
      game_over: moveResult.gameOver,
      winner: moveResult.winner
    })

  } catch (error) {
    console.error('Error processing bot move:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
