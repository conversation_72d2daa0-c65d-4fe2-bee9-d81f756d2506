import { createServerComponentClient } from '@/lib/supabase'
import { AuthServer } from '@/lib/auth'
import { notFound } from 'next/navigation'

interface ProfilePageProps {
  params: {
    username: string
  }
}

export default async function ProfilePage({ params }: ProfilePageProps) {
  const supabase = await createServerComponentClient()
  const { username } = params

  // Get profile by username
  const { profile, error: profileError } = await AuthServer.getProfileByUsername(username)
  
  if (profileError || !profile) {
    notFound()
  }

  // Get user stats
  const { stats } = await AuthServer.getUserStats(profile.id)

  // Get recent matches
  const { data: recentMatches, error: matchesError } = await supabase
    .from('matches')
    .select(`
      id,
      status,
      mode,
      winner_user_id,
      created_at,
      match_players!inner (
        user_id,
        color,
        score,
        profiles (username)
      )
    `)
    .eq('match_players.user_id', profile.id)
    .eq('status', 'finished')
    .order('created_at', { ascending: false })
    .limit(10)

  // Get most used deck
  let mostUsedDeck = null
  if (stats?.most_used_deck_id) {
    const { data: deckData } = await supabase
      .from('decks')
      .select('*')
      .eq('id', stats.most_used_deck_id)
      .single()
    
    mostUsedDeck = deckData
  }

  const totalGames = (stats?.wins || 0) + (stats?.losses || 0) + (stats?.draws || 0)
  const winRate = totalGames > 0 ? ((stats?.wins || 0) / totalGames * 100).toFixed(1) : '0.0'

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 bg-gray-700 rounded-full mx-auto mb-4 flex items-center justify-center">
            {profile.avatar_url ? (
              <img
                src={profile.avatar_url}
                alt={profile.username}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-2xl text-gray-400">
                {profile.username.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {profile.username}
          </h1>
          <p className="text-gray-400">
            Member since {new Date(profile.created_at).toLocaleDateString()}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Stats Summary */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Statistics</h2>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {stats?.wins || 0}
                </div>
                <div className="text-gray-400 text-sm">Wins</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">
                  {stats?.losses || 0}
                </div>
                <div className="text-gray-400 text-sm">Losses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  {stats?.draws || 0}
                </div>
                <div className="text-gray-400 text-sm">Draws</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {totalGames}
                </div>
                <div className="text-gray-400 text-sm">Total Games</div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-lg font-semibold text-white mb-1">
                Win Rate: {winRate}%
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${winRate}%` }}
                />
              </div>
            </div>
          </div>

          {/* Most Used Deck */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Most Used Deck</h2>
            
            {mostUsedDeck ? (
              <div className="flex items-center space-x-4">
                {mostUsedDeck.thumbnail_url ? (
                  <img
                    src={mostUsedDeck.thumbnail_url}
                    alt={mostUsedDeck.name}
                    className="w-16 h-16 rounded-lg object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = '/decks/placeholder.png'
                    }}
                  />
                ) : (
                  <div className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-sm">DECK</span>
                  </div>
                )}
                
                <div>
                  <h3 className="text-white font-semibold">{mostUsedDeck.name}</h3>
                  <p className="text-gray-400 text-sm">{mostUsedDeck.description}</p>
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-center py-8">
                No deck data available
              </div>
            )}
          </div>
        </div>

        {/* Recent Matches */}
        <div className="mt-8 bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Recent Matches</h2>
          
          {recentMatches && recentMatches.length > 0 ? (
            <div className="space-y-3">
              {recentMatches.map((match: any) => {
                const playerData = match.match_players.find((p: any) => p.user_id === profile.id)
                const opponentData = match.match_players.find((p: any) => p.user_id !== profile.id)
                
                const isWin = match.winner_user_id === profile.id
                const isDraw = match.winner_user_id === null
                const isLoss = !isWin && !isDraw

                return (
                  <div
                    key={match.id}
                    className={`
                      flex items-center justify-between p-4 rounded-lg border
                      ${isWin ? 'bg-green-900 border-green-700' : 
                        isDraw ? 'bg-yellow-900 border-yellow-700' : 
                        'bg-red-900 border-red-700'}
                    `}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`
                        w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold text-sm
                        ${isWin ? 'bg-green-600' : isDraw ? 'bg-yellow-600' : 'bg-red-600'}
                      `}>
                        {isWin ? 'W' : isDraw ? 'D' : 'L'}
                      </div>
                      
                      <div>
                        <div className="text-white font-medium">
                          vs {opponentData?.user_id ? opponentData.profiles?.username : 'Bot'}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {match.mode === 'pvp' ? 'Player vs Player' : 'Player vs Bot'}
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-white font-medium">
                        {playerData?.score || 0} - {opponentData?.score || 0}
                      </div>
                      <div className="text-gray-400 text-sm">
                        {new Date(match.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-gray-400 text-center py-8">
              No recent matches found
            </div>
          )}
        </div>

        {/* Back Button */}
        <div className="text-center mt-8">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  )
}
