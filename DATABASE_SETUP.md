# Database Setup Guide

This guide will help you set up the Supabase database for the Paragons Triad game.

## Prerequisites

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project in Supabase

## Setup Steps

### 1. Get Your Supabase Credentials

1. Go to your Supabase project dashboard
2. Navigate to Settings > API
3. Copy your Project URL and anon/public key
4. Update your `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

### 2. Run Database Migrations

In your Supabase project dashboard:

1. Go to the SQL Editor
2. Run each migration file in order:
   - `supabase/migrations/001_initial_schema.sql`
   - `supabase/migrations/002_rls_policies.sql`
   - `supabase/migrations/003_seed_data.sql`

### 3. Verify Setup

After running the migrations, you should see the following tables in your database:

- `profiles` - User profiles
- `stats` - User game statistics
- `cards` - Game cards with stats
- `decks` - Prebuilt card decks
- `deck_cards` - Junction table for deck-card relationships
- `matches` - Game matches
- `match_players` - Players in matches
- `moves` - Individual moves in matches

### 4. Enable Realtime (Optional)

For real-time game updates:

1. Go to Database > Replication in your Supabase dashboard
2. Enable realtime for the `matches` table
3. Enable realtime for the `moves` table

## Database Schema Overview

### Core Tables

- **profiles**: User accounts and basic info
- **stats**: Win/loss records and statistics
- **cards**: Individual game cards with N/E/S/W values
- **decks**: Prebuilt deck configurations
- **matches**: Game sessions with board state
- **match_players**: Player participation in matches
- **moves**: Individual card placements and captures

### Security

Row Level Security (RLS) is enabled on all tables with appropriate policies:

- Users can only modify their own profiles and stats
- Cards and decks are read-only for regular users
- Match data is readable by all, but only participants can modify
- Moves can only be created by match participants

## Seed Data

The database comes with:

- 7 Paragon cards with balanced stats
- 3 starter decks: Ironwall, Glass Cannon, and Brute Force
- Each deck contains 5 cards for gameplay

## Troubleshooting

If you encounter issues:

1. Check that all migrations ran successfully
2. Verify your environment variables are correct
3. Ensure RLS policies are properly applied
4. Check the Supabase logs for any errors
