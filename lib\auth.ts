import { createClientComponentClient, createServerComponentClient } from './supabase'
import { Database } from './types'

export type Profile = Database['public']['Tables']['profiles']['Row']

/**
 * Client-side authentication utilities
 */
export class AuthClient {
  private supabase = createClientComponentClient()

  /**
   * Sign in with magic link
   */
  async signInWithMagicLink(email: string) {
    const { data, error } = await this.supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    })

    return { data, error }
  }

  /**
   * Sign out
   */
  async signOut() {
    const { error } = await this.supabase.auth.signOut()
    return { error }
  }

  /**
   * Get current user
   */
  async getCurrentUser() {
    const { data: { user }, error } = await this.supabase.auth.getUser()
    return { user, error }
  }

  /**
   * Get current user's profile
   */
  async getCurrentProfile() {
    const { user } = await this.getCurrentUser()
    if (!user) return { profile: null, error: null }

    const { data: profile, error } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    return { profile, error }
  }

  /**
   * Create or update user profile
   */
  async upsertProfile(profile: Partial<Profile>) {
    const { user } = await this.getCurrentUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await this.supabase
      .from('profiles')
      .upsert({
        id: user.id,
        ...profile
      })
      .select()
      .single()

    return { data, error }
  }

  /**
   * Create initial stats record for user
   */
  async createUserStats() {
    const { user } = await this.getCurrentUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await this.supabase
      .from('stats')
      .upsert({
        user_id: user.id,
        wins: 0,
        losses: 0,
        draws: 0
      })
      .select()
      .single()

    return { data, error }
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback)
  }
}

/**
 * Server-side authentication utilities
 */
export class AuthServer {
  /**
   * Get current user on server
   */
  static async getCurrentUser() {
    const supabase = await createServerComponentClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  }

  /**
   * Get current user's profile on server
   */
  static async getCurrentProfile() {
    const supabase = await createServerComponentClient()
    const { user } = await this.getCurrentUser()
    
    if (!user) return { profile: null, error: null }

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    return { profile, error }
  }

  /**
   * Get user profile by username
   */
  static async getProfileByUsername(username: string) {
    const supabase = await createServerComponentClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .single()

    return { profile, error }
  }

  /**
   * Get user stats
   */
  static async getUserStats(userId: string) {
    const supabase = await createServerComponentClient()
    
    const { data: stats, error } = await supabase
      .from('stats')
      .select('*')
      .eq('user_id', userId)
      .single()

    return { stats, error }
  }
}

/**
 * Generate a unique username suggestion
 */
export function generateUsername(email: string): string {
  const baseUsername = email.split('@')[0].toLowerCase()
  const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${baseUsername}${randomSuffix}`
}

/**
 * Validate username format
 */
export function validateUsername(username: string): { valid: boolean; error?: string } {
  if (!username) {
    return { valid: false, error: 'Username is required' }
  }

  if (username.length < 3) {
    return { valid: false, error: 'Username must be at least 3 characters' }
  }

  if (username.length > 20) {
    return { valid: false, error: 'Username must be less than 20 characters' }
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    return { valid: false, error: 'Username can only contain letters, numbers, underscores, and hyphens' }
  }

  return { valid: true }
}

/**
 * Check if username is available
 */
export async function isUsernameAvailable(username: string): Promise<boolean> {
  const supabase = createClientComponentClient()
  
  const { data, error } = await supabase
    .from('profiles')
    .select('username')
    .eq('username', username)
    .single()

  // If no data found, username is available
  return !data && error?.code === 'PGRST116'
}
