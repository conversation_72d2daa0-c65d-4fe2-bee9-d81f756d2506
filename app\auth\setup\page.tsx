'use client'

import { useState, useEffect } from 'react'
import { AuthClient, generateUsername, validateUsername, isUsernameAvailable } from '@/lib/auth'
import { useRouter } from 'next/navigation'

export default function ProfileSetupPage() {
  const [username, setUsername] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [checkingUsername, setCheckingUsername] = useState(false)
  const [user, setUser] = useState<any>(null)
  
  const router = useRouter()
  const authClient = new AuthClient()

  useEffect(() => {
    // Check if user is authenticated
    const checkUser = async () => {
      const { user, error } = await authClient.getCurrentUser()
      if (error || !user) {
        router.push('/auth')
        return
      }
      
      setUser(user)
      
      // Generate a suggested username
      if (user.email) {
        const suggested = generateUsername(user.email)
        setUsername(suggested)
      }
    }

    checkUser()
  }, [])

  const checkUsernameAvailability = async (usernameToCheck: string) => {
    if (!usernameToCheck) return

    const validation = validateUsername(usernameToCheck)
    if (!validation.valid) {
      setError(validation.error || '')
      return
    }

    setCheckingUsername(true)
    setError('')

    try {
      const available = await isUsernameAvailable(usernameToCheck)
      if (!available) {
        setError('Username is already taken')
      }
    } catch (err) {
      setError('Error checking username availability')
    }

    setCheckingUsername(false)
  }

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUsername = e.target.value
    setUsername(newUsername)
    
    // Debounce username checking
    setTimeout(() => {
      if (newUsername === username) {
        checkUsernameAvailability(newUsername)
      }
    }, 500)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const validation = validateUsername(username)
    if (!validation.valid) {
      setError(validation.error || '')
      setLoading(false)
      return
    }

    try {
      // Create profile
      const { error: profileError } = await authClient.upsertProfile({
        username,
        avatar_url: null
      })

      if (profileError) {
        setError(profileError.message)
        setLoading(false)
        return
      }

      // Create initial stats
      await authClient.createUserStats()

      // Redirect to home
      router.push('/')
    } catch (err) {
      setError('Failed to create profile')
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to Paragons: Triad
          </h1>
          <p className="text-gray-300">
            Choose your username to get started
          </p>
        </div>

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              required
              value={username}
              onChange={handleUsernameChange}
              className="relative block w-full px-3 py-2 border border-gray-600 placeholder-gray-400 text-white bg-gray-800 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="Enter your username"
            />
            {checkingUsername && (
              <p className="text-gray-400 text-sm mt-1">Checking availability...</p>
            )}
          </div>

          {error && (
            <div className="text-red-400 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading || checkingUsername || !!error}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating Profile...' : 'Create Profile'}
          </button>
        </form>

        <div className="text-center">
          <p className="text-gray-400 text-sm">
            You can change your username later in your profile settings
          </p>
        </div>
      </div>
    </div>
  )
}
