import { createServerComponentClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { join_code } = body

    if (!join_code) {
      return NextResponse.json({ error: 'Join code is required' }, { status: 400 })
    }

    // Find the match by join code
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .select('*')
      .eq('join_code', join_code)
      .eq('status', 'waiting')
      .single()

    if (matchError || !match) {
      return NextResponse.json({ error: 'Match not found or no longer available' }, { status: 404 })
    }

    // Check if user is already in this match
    const { data: existingPlayer } = await supabase
      .from('match_players')
      .select('*')
      .eq('match_id', match.id)
      .eq('user_id', user.id)
      .single()

    if (existingPlayer) {
      return NextResponse.json({ match_id: match.id })
    }

    // Check if match is full
    const { data: players, error: playersError } = await supabase
      .from('match_players')
      .select('*')
      .eq('match_id', match.id)

    if (playersError) {
      console.error('Error checking players:', playersError)
      return NextResponse.json({ error: 'Failed to join match' }, { status: 500 })
    }

    if (players.length >= 2) {
      return NextResponse.json({ error: 'Match is full' }, { status: 400 })
    }

    // Add the player to the match
    const { error: joinError } = await supabase
      .from('match_players')
      .insert({
        match_id: match.id,
        user_id: user.id,
        is_host: false,
        color: 'B',
        score: 0
      })

    if (joinError) {
      console.error('Error joining match:', joinError)
      return NextResponse.json({ error: 'Failed to join match' }, { status: 500 })
    }

    return NextResponse.json({ match_id: match.id })

  } catch (error) {
    console.error('Error in match join:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
